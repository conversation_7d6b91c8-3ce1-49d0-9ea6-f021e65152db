# ADK Analyst - Enterprise DevOps Analytics Platform
# Environment Configuration Template
# Copy this file to .env and update with your values

# ============================================================================
# Google Cloud Configuration
# ============================================================================
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_GENAI_USE_VERTEXAI=true

# Google Cloud Authentication
GOOGLE_APPLICATION_CREDENTIALS=./service-account-key.json
SERVICE_ACCOUNT_KEY_PATH=./service-account-key.json

# ============================================================================
# Secret Manager Configuration (Recommended for Production)
# ============================================================================
# Enable Google Secret Manager for credential storage
USE_SECRET_MANAGER=true

# Secret Manager Secret Names (store actual credentials in Secret Manager)
JENKINS_CREDENTIALS_SECRET=jenkins-credentials
GITHUB_CREDENTIALS_SECRET=github-credentials
JENKINS_URL_SECRET=jenkins-url
GITHUB_TOKEN_SECRET=github-token

# ============================================================================
# Direct Environment Variables (Development/Testing Only)
# ============================================================================
# Jenkins Configuration (use Secret Manager in production)
JENKINS_URL=https://your-jenkins-server.com/
JENKINS_USERNAME=your-jenkins-username
JENKINS_PASSWORD=your-jenkins-password

# GitHub Configuration (use Secret Manager in production)
GITHUB_TOKEN=your-github-personal-access-token
GITHUB_PERSONAL_ACCESS_TOKEN=your-github-personal-access-token

# ============================================================================
# Security Configuration
# ============================================================================
ALLOWED_JENKINS_DOMAINS=your-jenkins-domain.com,localhost
ALLOWED_GITHUB_ORGS=your-org,your-other-org
ENABLE_AUDIT_LOGGING=true
ENABLE_RATE_LIMITING=true
ENABLE_SECURITY_MONITORING=true

# ============================================================================
# Application Configuration
# ============================================================================
LOG_LEVEL=INFO
MAX_CONCURRENT_REQUESTS=10
RATE_LIMIT_PER_MINUTE=100
MAX_RESULTS_PER_QUERY=1000

# ============================================================================
# Performance Configuration
# ============================================================================
CONNECTION_TIMEOUT=30
READ_TIMEOUT=60
MAX_RETRIES=3
ENABLE_CACHING=true
CACHE_TTL_SECONDS=300

# ============================================================================
# Development Configuration
# ============================================================================
DEBUG=false
TESTING=false
DEVELOPMENT_MODE=false

# ============================================================================
# AI Model Configuration
# ============================================================================
GEMINI_PRO_MODEL=gemini-2.5-pro-preview-05-06
GEMINI_FLASH_MODEL=gemini-2.5-flash-preview-05-20
DEFAULT_MODEL=gemini-2.5-pro-preview-05-06

# ============================================================================
# Web Interface Configuration
# ============================================================================
WEB_PORT=8010
WEB_HOST=0.0.0.0
ENABLE_WEB_INTERFACE=true

# ============================================================================
# Monitoring and Observability
# ============================================================================
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_HEALTH_CHECKS=true
METRICS_EXPORT_INTERVAL=60
