# Google Secret Manager Integration

This document explains how to configure ADK Analyst to use Google Secret Manager for secure credential storage instead of environment variables.

## 🔒 Why Use Secret Manager?

### Benefits
- **Enhanced Security**: Credentials are encrypted at rest and in transit
- **Centralized Management**: Single source of truth for all credentials
- **Access Control**: Fine-grained IAM permissions
- **Audit Logging**: Complete audit trail of credential access
- **Rotation Support**: Easy credential rotation without code changes
- **Environment Isolation**: Different credentials for dev/staging/prod

### Enterprise Features
- **Compliance**: SOC 2, GDPR, and other compliance requirements
- **Integration**: Works with Google Cloud IAM and monitoring
- **Scalability**: Handles high-volume credential requests
- **Reliability**: 99.95% SLA with automatic failover

## 🚀 Quick Setup

### 1. Enable Secret Manager API
```bash
# Enable the Secret Manager API
gcloud services enable secretmanager.googleapis.com

# Verify the API is enabled
gcloud services list --enabled --filter="name:secretmanager"
```

### 2. Set Up IAM Permissions
```bash
# Grant Secret Manager access to your service account
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:YOUR_SERVICE_ACCOUNT@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/secretmanager.secretAccessor"

# For secret creation (optional, for setup script)
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:YOUR_SERVICE_ACCOUNT@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/secretmanager.admin"
```

### 3. Configure Environment Variables
Update your `.env` file:
```bash
# Enable Secret Manager
USE_SECRET_MANAGER=true

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=./service-account-key.json

# Keep environment variables as fallback
JENKINS_URL=https://your-jenkins-server.com/
JENKINS_USERNAME=your-username
JENKINS_PASSWORD=your-password
GITHUB_TOKEN=your-github-token
```

### 4. Run Setup Script
```bash
# Setup all credentials
python scripts/setup_secrets.py

# Setup only Jenkins credentials
python scripts/setup_secrets.py --jenkins-only

# Setup only GitHub credentials
python scripts/setup_secrets.py --github-only

# Verify existing secrets
python scripts/setup_secrets.py --verify-only
```

## 📋 Manual Secret Creation

### Jenkins Credentials
```bash
# Create the secret
gcloud secrets create jenkins-credentials --replication-policy="automatic"

# Add the secret value
echo '{
  "url": "https://your-jenkins-server.com/",
  "username": "your-username",
  "password": "your-password",
  "token": "your-api-token",
  "service_name": "jenkins"
}' | gcloud secrets versions add jenkins-credentials --data-file=-
```

### GitHub Credentials
```bash
# Create the secret
gcloud secrets create github-credentials --replication-policy="automatic"

# Add the secret value
echo '{
  "token": "your-github-token",
  "personal_access_token": "your-github-token",
  "service_name": "github"
}' | gcloud secrets versions add github-credentials --data-file=-
```

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `USE_SECRET_MANAGER` | Enable Secret Manager integration | `false` |
| `JENKINS_CREDENTIALS_SECRET` | Jenkins secret name | `jenkins-credentials` |
| `GITHUB_CREDENTIALS_SECRET` | GitHub secret name | `github-credentials` |
| `GOOGLE_CLOUD_PROJECT` | Google Cloud project ID | Required |

### Secret Structure

#### Jenkins Credentials
```json
{
  "url": "https://jenkins.example.com/",
  "username": "jenkins-user",
  "password": "jenkins-password",
  "token": "jenkins-api-token",
  "service_name": "jenkins"
}
```

#### GitHub Credentials
```json
{
  "token": "ghp_xxxxxxxxxxxxxxxxxxxx",
  "personal_access_token": "ghp_xxxxxxxxxxxxxxxxxxxx",
  "service_name": "github"
}
```

## 🔄 Fallback Mechanism

The system uses a robust fallback mechanism:

1. **Secret Manager** (if `USE_SECRET_MANAGER=true`)
2. **Environment Variables** (if Secret Manager fails)
3. **Test Credentials** (for development/testing)

This ensures the system continues to work even if Secret Manager is unavailable.

## 🛠️ Development Workflow

### Local Development
```bash
# Use environment variables for local development
USE_SECRET_MANAGER=false
JENKINS_URL=http://localhost:8080
JENKINS_USERNAME=admin
JENKINS_PASSWORD=admin
GITHUB_TOKEN=your-dev-token
```

### Staging Environment
```bash
# Use Secret Manager with staging credentials
USE_SECRET_MANAGER=true
GOOGLE_CLOUD_PROJECT=your-staging-project
```

### Production Environment
```bash
# Use Secret Manager with production credentials
USE_SECRET_MANAGER=true
GOOGLE_CLOUD_PROJECT=your-production-project
```

## 🔍 Monitoring and Troubleshooting

### Verify Secret Access
```bash
# Test secret access
gcloud secrets versions access latest --secret="jenkins-credentials"
gcloud secrets versions access latest --secret="github-credentials"
```

### Check IAM Permissions
```bash
# Check service account permissions
gcloud projects get-iam-policy YOUR_PROJECT_ID \
    --flatten="bindings[].members" \
    --format="table(bindings.role)" \
    --filter="bindings.members:YOUR_SERVICE_ACCOUNT@YOUR_PROJECT_ID.iam.gserviceaccount.com"
```

### Debug Logs
The system provides comprehensive logging for Secret Manager operations:
```python
# Enable debug logging
LOG_LEVEL=DEBUG

# Check logs for Secret Manager operations
# Look for messages like:
# "Retrieving credentials from Secret Manager"
# "Using credentials from environment variables as fallback"
```

## 🔐 Security Best Practices

### 1. Principle of Least Privilege
- Grant only `secretmanager.secretAccessor` role for runtime
- Use `secretmanager.admin` only for setup/management

### 2. Secret Rotation
```bash
# Rotate Jenkins credentials
echo '{"url": "...", "username": "...", "password": "NEW_PASSWORD"}' | \
    gcloud secrets versions add jenkins-credentials --data-file=-

# Rotate GitHub token
echo '{"token": "NEW_TOKEN", "service_name": "github"}' | \
    gcloud secrets versions add github-credentials --data-file=-
```

### 3. Audit Logging
Enable Cloud Audit Logs to track secret access:
```bash
# Enable audit logs for Secret Manager
gcloud logging sinks create secret-manager-audit \
    bigquery.googleapis.com/projects/YOUR_PROJECT/datasets/audit_logs \
    --log-filter='protoPayload.serviceName="secretmanager.googleapis.com"'
```

### 4. Network Security
- Use VPC Service Controls for additional network isolation
- Implement Private Google Access for GKE/Compute Engine

## 🚨 Troubleshooting

### Common Issues

#### Permission Denied
```
Error: Permission denied accessing secret 'jenkins-credentials'
```
**Solution**: Check IAM permissions and ensure service account has `secretmanager.secretAccessor` role.

#### Secret Not Found
```
Error: Secret 'jenkins-credentials' not found
```
**Solution**: Create the secret using the setup script or manual commands above.

#### Invalid JSON
```
Error: Secret contains invalid JSON
```
**Solution**: Verify secret format matches the expected structure.

#### Authentication Failed
```
Error: Failed to create Secret Manager client
```
**Solution**: Check `GOOGLE_APPLICATION_CREDENTIALS` and service account key file.

### Getting Help
1. Check the application logs for detailed error messages
2. Verify Google Cloud project and IAM configuration
3. Test secret access using `gcloud` CLI
4. Review the fallback mechanism - environment variables should work as backup

## 📚 Additional Resources

- [Google Secret Manager Documentation](https://cloud.google.com/secret-manager/docs)
- [IAM Best Practices](https://cloud.google.com/iam/docs/using-iam-securely)
- [Secret Manager Client Libraries](https://cloud.google.com/secret-manager/docs/reference/libraries)
- [Audit Logging](https://cloud.google.com/logging/docs/audit)
