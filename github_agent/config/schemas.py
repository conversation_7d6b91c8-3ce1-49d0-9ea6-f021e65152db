"""Response schemas for GitHub Reader Agent."""

from datetime import datetime
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field


class ErrorInfo(BaseModel):
    """Error information model."""
    code: str = Field(description="Error code")
    message: str = Field(description="Error message")
    details: Dict[str, Any] = Field(default_factory=dict, description="Additional error details")


class ResponseMetadata(BaseModel):
    """Response metadata model."""
    timestamp: str = Field(description="Response timestamp in ISO format")
    github_url: Optional[str] = Field(default=None, description="GitHub API URL")
    total_records: int = Field(default=0, description="Total number of records")
    query_duration_ms: int = Field(default=0, description="Query duration in milliseconds")
    api_version: str = Field(default="2022-11-28", description="GitHub API version used")
    rate_limit_remaining: Optional[int] = Field(default=None, description="Remaining API calls")
    rate_limit_reset: Optional[int] = Field(default=None, description="Rate limit reset timestamp")
    cached: bool = Field(default=False, description="Whether response was served from cache")


class GitHubResponse(BaseModel):
    """Standard response model for GitHub operations."""
    status: str = Field(description="Response status: success, error, or warning")
    data: Dict[str, Any] = Field(default_factory=dict, description="Response data")
    metadata: ResponseMetadata = Field(description="Response metadata")
    errors: List[ErrorInfo] = Field(default_factory=list, description="List of errors")
    warnings: List[str] = Field(default_factory=list, description="List of warnings")


class CommitInfo(BaseModel):
    """Commit information model."""
    sha: str = Field(description="Commit SHA hash")
    message: str = Field(description="Commit message")
    author_name: str = Field(description="Author name")
    author_email: str = Field(description="Author email")
    author_date: str = Field(description="Author date in ISO format")
    committer_name: str = Field(description="Committer name")
    committer_email: str = Field(description="Committer email")
    committer_date: str = Field(description="Committer date in ISO format")
    url: str = Field(description="Commit URL")
    additions: int = Field(default=0, description="Lines added")
    deletions: int = Field(default=0, description="Lines deleted")
    total_changes: int = Field(default=0, description="Total changes")
    files_changed: int = Field(default=0, description="Number of files changed")
    parents: List[str] = Field(default_factory=list, description="Parent commit SHAs")
    is_merge: bool = Field(default=False, description="Whether this is a merge commit")


class PullRequestInfo(BaseModel):
    """Pull request information model."""
    number: int = Field(description="Pull request number")
    title: str = Field(description="Pull request title")
    body: Optional[str] = Field(default=None, description="Pull request body")
    state: str = Field(description="Pull request state")
    author: str = Field(description="Pull request author")
    created_at: str = Field(description="Creation date in ISO format")
    updated_at: str = Field(description="Last update date in ISO format")
    merged_at: Optional[str] = Field(default=None, description="Merge date in ISO format")
    closed_at: Optional[str] = Field(default=None, description="Close date in ISO format")
    base_branch: str = Field(description="Base branch name")
    head_branch: str = Field(description="Head branch name")
    url: str = Field(description="Pull request URL")
    draft: bool = Field(default=False, description="Whether PR is draft")
    mergeable: Optional[bool] = Field(default=None, description="Whether PR is mergeable")
    commits_count: int = Field(default=0, description="Number of commits")
    additions: int = Field(default=0, description="Lines added")
    deletions: int = Field(default=0, description="Lines deleted")
    changed_files: int = Field(default=0, description="Number of files changed")
    review_comments: int = Field(default=0, description="Number of review comments")
    comments: int = Field(default=0, description="Number of comments")


class ReleaseInfo(BaseModel):
    """Release information model."""
    id: int = Field(description="Release ID")
    tag_name: str = Field(description="Tag name")
    name: str = Field(description="Release name")
    body: Optional[str] = Field(default=None, description="Release notes")
    draft: bool = Field(default=False, description="Whether release is draft")
    prerelease: bool = Field(default=False, description="Whether release is prerelease")
    created_at: str = Field(description="Creation date in ISO format")
    published_at: Optional[str] = Field(default=None, description="Publication date in ISO format")
    author: str = Field(description="Release author")
    url: str = Field(description="Release URL")
    assets_count: int = Field(default=0, description="Number of assets")
    target_commitish: str = Field(description="Target commit or branch")


class BranchInfo(BaseModel):
    """Branch information model."""
    name: str = Field(description="Branch name")
    sha: str = Field(description="Latest commit SHA")
    protected: bool = Field(default=False, description="Whether branch is protected")
    default: bool = Field(default=False, description="Whether this is the default branch")
    ahead_by: Optional[int] = Field(default=None, description="Commits ahead of default branch")
    behind_by: Optional[int] = Field(default=None, description="Commits behind default branch")
    last_commit_date: Optional[str] = Field(default=None, description="Last commit date")
    last_commit_author: Optional[str] = Field(default=None, description="Last commit author")


class WorkflowInfo(BaseModel):
    """GitHub workflow information model."""
    id: int = Field(description="Workflow ID")
    name: str = Field(description="Workflow name")
    path: str = Field(description="Workflow file path")
    state: str = Field(description="Workflow state")
    created_at: str = Field(description="Creation date in ISO format")
    updated_at: str = Field(description="Last update date in ISO format")
    url: str = Field(description="Workflow URL")
    badge_url: str = Field(description="Workflow badge URL")


class WorkflowRunInfo(BaseModel):
    """GitHub workflow run information model."""
    id: int = Field(description="Workflow run ID")
    name: str = Field(description="Workflow run name")
    head_branch: str = Field(description="Head branch")
    head_sha: str = Field(description="Head commit SHA")
    status: str = Field(description="Run status")
    conclusion: Optional[str] = Field(default=None, description="Run conclusion")
    created_at: str = Field(description="Creation date in ISO format")
    updated_at: str = Field(description="Last update date in ISO format")
    run_started_at: Optional[str] = Field(default=None, description="Run start date")
    url: str = Field(description="Workflow run URL")
    jobs_url: str = Field(description="Jobs URL")
    logs_url: str = Field(description="Logs URL")
    event: str = Field(description="Triggering event")
    actor: str = Field(description="Actor who triggered the run")


class RepositoryMetrics(BaseModel):
    """Repository metrics model."""
    total_commits: int = Field(description="Total number of commits")
    total_contributors: int = Field(description="Total number of contributors")
    total_pull_requests: int = Field(description="Total number of pull requests")
    total_releases: int = Field(description="Total number of releases")
    total_branches: int = Field(description="Total number of branches")
    total_tags: int = Field(description="Total number of tags")
    open_issues: int = Field(description="Number of open issues")
    closed_issues: int = Field(description="Number of closed issues")
    forks_count: int = Field(description="Number of forks")
    stars_count: int = Field(description="Number of stars")
    watchers_count: int = Field(description="Number of watchers")
    size_kb: int = Field(description="Repository size in KB")
    default_branch: str = Field(description="Default branch name")
    language: Optional[str] = Field(default=None, description="Primary language")
    created_at: str = Field(description="Repository creation date")
    updated_at: str = Field(description="Last update date")
    pushed_at: str = Field(description="Last push date")


class TeamMetrics(BaseModel):
    """Team collaboration metrics model."""
    active_contributors: int = Field(description="Number of active contributors")
    total_commits_period: int = Field(description="Total commits in analysis period")
    average_commits_per_contributor: float = Field(description="Average commits per contributor")
    commit_frequency_per_day: float = Field(description="Average commits per day")
    pull_request_frequency: float = Field(description="Average PRs per day")
    average_pr_size: float = Field(description="Average PR size (lines changed)")
    average_pr_review_time_hours: float = Field(description="Average PR review time in hours")
    code_review_participation: float = Field(description="Percentage of PRs with reviews")
    collaboration_score: float = Field(description="Overall collaboration score (0-100)")


class FileChangeInfo(BaseModel):
    """File change information model."""
    filename: str = Field(description="File name")
    status: str = Field(description="Change status (added, modified, deleted, renamed)")
    additions: int = Field(default=0, description="Lines added")
    deletions: int = Field(default=0, description="Lines deleted")
    changes: int = Field(default=0, description="Total changes")
    blob_url: Optional[str] = Field(default=None, description="Blob URL")
    raw_url: Optional[str] = Field(default=None, description="Raw file URL")
    contents_url: Optional[str] = Field(default=None, description="Contents API URL")
    patch: Optional[str] = Field(default=None, description="Patch content (truncated)")


class CommitDetails(BaseModel):
    """Detailed commit information model."""
    sha: str = Field(description="Commit SHA hash")
    node_id: str = Field(description="Node ID")
    url: str = Field(description="HTML URL")
    api_url: str = Field(description="API URL")
    message: str = Field(description="Commit message")
    author: Dict[str, Any] = Field(description="Author information")
    committer: Dict[str, Any] = Field(description="Committer information")
    github_author: Optional[Dict[str, Any]] = Field(default=None, description="GitHub author info")
    github_committer: Optional[Dict[str, Any]] = Field(default=None, description="GitHub committer info")
    parents: List[Dict[str, str]] = Field(default_factory=list, description="Parent commits")
    stats: Dict[str, int] = Field(description="Commit statistics")
    files: List[FileChangeInfo] = Field(default_factory=list, description="Changed files")
    verification: Dict[str, Any] = Field(default_factory=dict, description="Verification info")
    tree: Dict[str, str] = Field(description="Tree information")


class ReviewInfo(BaseModel):
    """Pull request review information model."""
    id: int = Field(description="Review ID")
    state: str = Field(description="Review state (approved, changes_requested, commented)")
    body: str = Field(description="Review body")
    submitted_at: Optional[str] = Field(default=None, description="Submission date")
    user: Dict[str, Any] = Field(description="Reviewer information")


class PullRequestDetails(BaseModel):
    """Detailed pull request information model."""
    number: int = Field(description="Pull request number")
    title: str = Field(description="Pull request title")
    body: str = Field(description="Pull request body")
    state: str = Field(description="Pull request state")
    draft: bool = Field(default=False, description="Whether PR is draft")
    merged: bool = Field(default=False, description="Whether PR is merged")
    mergeable: Optional[bool] = Field(default=None, description="Whether PR is mergeable")
    mergeable_state: Optional[str] = Field(default=None, description="Mergeable state")
    url: str = Field(description="HTML URL")
    api_url: str = Field(description="API URL")
    created_at: str = Field(description="Creation date")
    updated_at: str = Field(description="Last update date")
    closed_at: Optional[str] = Field(default=None, description="Close date")
    merged_at: Optional[str] = Field(default=None, description="Merge date")
    author: Dict[str, Any] = Field(description="Author information")
    assignees: List[Dict[str, Any]] = Field(default_factory=list, description="Assignees")
    labels: List[Dict[str, Any]] = Field(default_factory=list, description="Labels")
    milestone: Optional[Dict[str, Any]] = Field(default=None, description="Milestone")
    head: Dict[str, Any] = Field(description="Head branch information")
    base: Dict[str, Any] = Field(description="Base branch information")
    stats: Dict[str, int] = Field(description="PR statistics")
    reviews: List[ReviewInfo] = Field(default_factory=list, description="Reviews")
    commits: Optional[List[Dict[str, Any]]] = Field(default=None, description="Commits (if requested)")


class AssetInfo(BaseModel):
    """Release asset information model."""
    id: int = Field(description="Asset ID")
    name: str = Field(description="Asset name")
    label: Optional[str] = Field(default=None, description="Asset label")
    content_type: str = Field(description="Content type")
    size: int = Field(description="Asset size in bytes")
    download_count: int = Field(default=0, description="Download count")
    created_at: str = Field(description="Creation date")
    updated_at: str = Field(description="Last update date")
    browser_download_url: str = Field(description="Download URL")
    uploader: Optional[Dict[str, Any]] = Field(default=None, description="Uploader information")


class ReleaseDetails(BaseModel):
    """Detailed release information model."""
    id: int = Field(description="Release ID")
    tag_name: str = Field(description="Tag name")
    target_commitish: str = Field(description="Target commit or branch")
    name: str = Field(description="Release name")
    body: str = Field(description="Release notes")
    draft: bool = Field(default=False, description="Whether release is draft")
    prerelease: bool = Field(default=False, description="Whether release is prerelease")
    created_at: str = Field(description="Creation date")
    published_at: Optional[str] = Field(default=None, description="Publication date")
    url: str = Field(description="HTML URL")
    api_url: str = Field(description="API URL")
    tarball_url: str = Field(description="Tarball URL")
    zipball_url: str = Field(description="Zipball URL")
    author: Optional[Dict[str, Any]] = Field(default=None, description="Author information")
    assets: List[AssetInfo] = Field(default_factory=list, description="Release assets")
    assets_count: int = Field(description="Number of assets")
    total_downloads: int = Field(default=0, description="Total downloads")


class TagInfo(BaseModel):
    """Tag information model."""
    name: str = Field(description="Tag name")
    commit: Dict[str, str] = Field(description="Commit information")
    zipball_url: str = Field(description="Zipball URL")
    tarball_url: str = Field(description="Tarball URL")
    node_id: str = Field(description="Node ID")


class BranchProtectionInfo(BaseModel):
    """Branch protection information model."""
    enabled: bool = Field(description="Whether protection is enabled")
    required_status_checks: Optional[Dict[str, Any]] = Field(default=None, description="Required status checks")
    enforce_admins: bool = Field(default=False, description="Enforce for admins")
    required_pull_request_reviews: Optional[Dict[str, Any]] = Field(default=None, description="Required PR reviews")
    restrictions: Optional[Dict[str, Any]] = Field(default=None, description="Push restrictions")
    allow_force_pushes: bool = Field(default=False, description="Allow force pushes")
    allow_deletions: bool = Field(default=False, description="Allow deletions")
    block_creations: bool = Field(default=False, description="Block creations")
    required_conversation_resolution: bool = Field(default=False, description="Require conversation resolution")


class BranchDetails(BaseModel):
    """Detailed branch information model."""
    name: str = Field(description="Branch name")
    commit: Dict[str, Any] = Field(description="Latest commit information")
    protected: bool = Field(default=False, description="Whether branch is protected")
    protection: Optional[BranchProtectionInfo] = Field(default=None, description="Protection details")
    is_default: bool = Field(default=False, description="Whether this is the default branch")
    is_stale: bool = Field(default=False, description="Whether branch is stale")
    branch_type: str = Field(description="Branch type classification")
    links: Dict[str, Any] = Field(default_factory=dict, description="Branch links")


class RepositoryStatistics(BaseModel):
    """Repository statistics model."""
    total_commits: int = Field(description="Total commits")
    total_contributors: int = Field(description="Total contributors")
    total_pull_requests: int = Field(description="Total pull requests")
    total_releases: int = Field(description="Total releases")
    total_branches: int = Field(description="Total branches")
    total_tags: int = Field(description="Total tags")
    open_issues: int = Field(description="Open issues")
    closed_issues: int = Field(description="Closed issues")
    forks_count: int = Field(description="Forks count")
    stars_count: int = Field(description="Stars count")
    watchers_count: int = Field(description="Watchers count")
    size_kb: int = Field(description="Repository size in KB")
    health_score: float = Field(description="Repository health score (0-100)")

