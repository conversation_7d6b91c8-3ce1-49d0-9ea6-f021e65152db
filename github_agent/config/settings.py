"""Configuration settings for GitHub Reader Agent."""

import os
from functools import lru_cache
from typing import List, Optional

try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings

from pydantic import Field


class GitHubAgentSettings(BaseSettings):
    """GitHub Agent configuration settings."""

    model_config = {"extra": "ignore"}  # Ignore extra environment variables

    # Environment
    environment: str = Field(default="development", env="ENVIRONMENT")
    testing: bool = Field(default=False, env="TESTING")
    debug: bool = Field(default=False, env="DEBUG")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Google Cloud Configuration
    google_cloud_project: str = Field(default="", env="GOOGLE_CLOUD_PROJECT")
    
    # Gemini Model Configuration
    gemini_pro_model: str = Field(
        default="gemini-2.0-flash-exp",
        env="GEMINI_PRO_MODEL"
    )
    gemini_flash_model: str = Field(
        default="gemini-2.0-flash-exp", 
        env="GEMINI_FLASH_MODEL"
    )
    
    # GitHub Configuration
    github_credentials_secret: str = Field(
        default="github-credentials", env="GITHUB_CREDENTIALS_SECRET"
    )
    allowed_github_domains: str = Field(
        default="github.com,api.github.com",
        env="ALLOWED_GITHUB_DOMAINS"
    )
    github_api_version: str = Field(
        default="2022-11-28",
        env="GITHUB_API_VERSION"
    )
    
    # Rate Limiting Configuration
    github_requests_per_hour: int = Field(default=5000, env="GITHUB_REQUESTS_PER_HOUR")
    github_requests_per_minute: int = Field(default=100, env="GITHUB_REQUESTS_PER_MINUTE")
    enable_rate_limiting: bool = Field(default=True, env="ENABLE_RATE_LIMITING")
    
    # Cache Configuration
    enable_caching: bool = Field(default=True, env="ENABLE_CACHING")
    cache_ttl_seconds: int = Field(default=300, env="CACHE_TTL_SECONDS")
    max_cache_size: int = Field(default=1000, env="MAX_CACHE_SIZE")
    
    # Performance Configuration
    max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")
    request_timeout_seconds: int = Field(default=30, env="REQUEST_TIMEOUT_SECONDS")
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    
    # Pagination Configuration
    default_page_size: int = Field(default=30, env="DEFAULT_PAGE_SIZE")
    max_page_size: int = Field(default=100, env="MAX_PAGE_SIZE")
    max_total_items: int = Field(default=1000, env="MAX_TOTAL_ITEMS")
    
    # Security Configuration
    enable_input_validation: bool = Field(default=True, env="ENABLE_INPUT_VALIDATION")
    enable_audit_logging: bool = Field(default=True, env="ENABLE_AUDIT_LOGGING")
    max_input_length: int = Field(default=1000, env="MAX_INPUT_LENGTH")
    
    # DORA Metrics Configuration
    default_analysis_days: int = Field(default=30, env="DEFAULT_ANALYSIS_DAYS")
    max_analysis_days: int = Field(default=365, env="MAX_ANALYSIS_DAYS")
    deployment_branch_patterns: str = Field(
        default="main,master,production,prod,release",
        env="DEPLOYMENT_BRANCH_PATTERNS"
    )
    hotfix_branch_patterns: str = Field(
        default="hotfix,fix,patch",
        env="HOTFIX_BRANCH_PATTERNS"
    )
    
    # Team Analytics Configuration
    enable_team_analytics: bool = Field(default=True, env="ENABLE_TEAM_ANALYTICS")
    min_commits_for_analysis: int = Field(default=5, env="MIN_COMMITS_FOR_ANALYSIS")
    collaboration_threshold_days: int = Field(default=7, env="COLLABORATION_THRESHOLD_DAYS")
    



@lru_cache()
def get_settings() -> GitHubAgentSettings:
    """Get cached settings instance."""
    return GitHubAgentSettings()


def get_allowed_github_domains() -> List[str]:
    """Get list of allowed GitHub domains."""
    settings = get_settings()
    return [domain.strip() for domain in settings.allowed_github_domains.split(",")]


def validate_github_url(url: str) -> bool:
    """Validate if a GitHub URL is allowed."""
    from urllib.parse import urlparse
    
    parsed = urlparse(url)
    domain = parsed.hostname
    
    if not domain:
        return False
    
    return domain in get_allowed_github_domains()


def get_deployment_branch_patterns() -> List[str]:
    """Get list of deployment branch patterns."""
    settings = get_settings()
    return [pattern.strip() for pattern in settings.deployment_branch_patterns.split(",")]


def get_hotfix_branch_patterns() -> List[str]:
    """Get list of hotfix branch patterns."""
    settings = get_settings()
    return [pattern.strip() for pattern in settings.hotfix_branch_patterns.split(",")]


def is_production() -> bool:
    """Check if running in production environment."""
    return get_settings().environment.lower() == "production"


def is_testing() -> bool:
    """Check if running in testing mode."""
    return get_settings().testing


def get_rate_limit_config() -> dict:
    """Get rate limiting configuration."""
    settings = get_settings()
    return {
        "enabled": settings.enable_rate_limiting,
        "requests_per_hour": settings.github_requests_per_hour,
        "requests_per_minute": settings.github_requests_per_minute
    }


def get_cache_config() -> dict:
    """Get cache configuration."""
    settings = get_settings()
    return {
        "enabled": settings.enable_caching,
        "ttl_seconds": settings.cache_ttl_seconds,
        "max_size": settings.max_cache_size
    }


def get_performance_config() -> dict:
    """Get performance configuration."""
    settings = get_settings()
    return {
        "max_concurrent_requests": settings.max_concurrent_requests,
        "request_timeout_seconds": settings.request_timeout_seconds,
        "max_retries": settings.max_retries
    }


def get_pagination_config() -> dict:
    """Get pagination configuration."""
    settings = get_settings()
    return {
        "default_page_size": settings.default_page_size,
        "max_page_size": settings.max_page_size,
        "max_total_items": settings.max_total_items
    }


def get_security_config() -> dict:
    """Get security configuration."""
    settings = get_settings()
    return {
        "enable_input_validation": settings.enable_input_validation,
        "enable_audit_logging": settings.enable_audit_logging,
        "max_input_length": settings.max_input_length
    }


def get_dora_metrics_config() -> dict:
    """Get DORA metrics configuration."""
    settings = get_settings()
    return {
        "default_analysis_days": settings.default_analysis_days,
        "max_analysis_days": settings.max_analysis_days,
        "deployment_branch_patterns": get_deployment_branch_patterns(),
        "hotfix_branch_patterns": get_hotfix_branch_patterns()
    }


def get_team_analytics_config() -> dict:
    """Get team analytics configuration."""
    settings = get_settings()
    return {
        "enabled": settings.enable_team_analytics,
        "min_commits_for_analysis": settings.min_commits_for_analysis,
        "collaboration_threshold_days": settings.collaboration_threshold_days
    }
