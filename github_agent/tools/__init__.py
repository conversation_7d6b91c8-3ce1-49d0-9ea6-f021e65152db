"""Tools module for GitHub Reader Agent."""

from .github_client import GitHubClientWrapper
from .connection_tools import validate_github_connection
from .repository_tools import get_repository_info, get_repository_metrics
from .commit_tools import get_commits_with_metadata, get_commit_details
from .pr_tools import get_pull_requests_data
from .release_tools import get_releases_and_tags
from .branch_tools import get_branch_analysis

__all__ = [
    # Core infrastructure
    "GitHubClientWrapper",
    "validate_github_connection",

    # Repository tools
    "get_repository_info",
    "get_repository_metrics",

    # Commit tools
    "get_commits_with_metadata",
    "get_commit_details",

    # Pull request tools
    "get_pull_requests_data",

    # Release tools
    "get_releases_and_tags",

    # Branch tools
    "get_branch_analysis"
]
