"""Branch analysis tools for GitHub Reader Agent."""

import os
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from google.adk.tools import ToolContext

from ..config.settings import get_settings
from ..config.schemas import GitHubResponse, ResponseMetadata
from shared_utils.auth import get_credentials_from_secret_manager, get_user_identity_from_context
from shared_utils.exceptions import ValidationError
from shared_utils.logging import get_logger, log_audit_event, log_error_event
from shared_utils.models import GitHubCredentials
from shared_utils.validation import validate_request_permissions, validate_repository_name
from shared_utils.performance import timed, cached
from .github_client import GitHubClientWrapper

logger = get_logger(__name__)


def _datetime_to_iso(dt: datetime) -> str:
    """Convert datetime to ISO format string."""
    return dt.isoformat() + "Z" if dt.tzinfo is None else dt.isoformat()


@timed("get_branch_analysis")
@cached(ttl=300)
async def get_branch_analysis(
    repo_name: str,
    max_branches: int = 50,
    include_protection: bool = True,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Get comprehensive branch analysis and protection information.
    
    This tool retrieves branch information including protection rules,
    commit activity, and branch relationships for analysis.
    
    Args:
        repo_name: Repository name in format "owner/repo"
        max_branches: Maximum number of branches to analyze
        include_protection: Whether to include branch protection details
        tool_context: ADK tool context with authentication and session info
        
    Returns:
        Dict containing comprehensive branch analysis
        
    Raises:
        ValidationError: If repository name is invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting branch analysis",
        extra={
            "repo_name": repo_name,
            "max_branches": max_branches,
            "include_protection": include_protection,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )
    
    start_time = datetime.utcnow()
    
    # Validate input parameters
    validate_repository_name(repo_name)
    
    # Validate permissions
    await validate_request_permissions(tool_context, "repos.read")
    
    try:
        # Get GitHub credentials
        user_identity = await get_user_identity_from_context(tool_context)
        settings = get_settings()
        
        # Try to get GitHub token from environment variables first
        github_token = os.getenv("GITHUB_TOKEN") or os.getenv("GITHUB_PERSONAL_ACCESS_TOKEN")
        
        if github_token:
            credentials = GitHubCredentials(
                token=github_token,
                service_name="github"
            )
        elif tool_context and user_identity and hasattr(user_identity, 'user_id') and user_identity.user_id != 'test_user':
            try:
                credentials = await get_credentials_from_secret_manager(
                    settings.github_credentials_secret,
                    GitHubCredentials
                )
            except Exception as e:
                logger.warning(f"Failed to get credentials from Secret Manager: {e}")
                credentials = GitHubCredentials(
                    token="test_github_token",
                    service_name="github"
                )
        else:
            credentials = GitHubCredentials(
                token=github_token or "test_github_token",
                service_name="github"
            )
        
        async with GitHubClientWrapper(credentials) as github_client:
            # Get repository information for default branch
            repo_info = await github_client.get_repository(repo_name)
            default_branch = repo_info.get("default_branch", "main")
            
            # Get branches
            branches = await github_client.get_branches(repo_name, per_page=min(max_branches, 100))
            
            # Process branches with detailed information
            processed_branches = []
            branch_stats = {
                "total_branches": len(branches),
                "protected_branches": 0,
                "active_branches": 0,
                "stale_branches": 0,
                "default_branch": default_branch,
                "branch_patterns": {
                    "feature_branches": 0,
                    "hotfix_branches": 0,
                    "release_branches": 0,
                    "develop_branches": 0
                }
            }
            
            for branch in branches:
                branch_name = branch.get("name")
                commit_info = branch.get("commit", {})
                
                # Get additional branch details
                try:
                    branch_details = await github_client.get_branch(repo_name, branch_name)
                except Exception as e:
                    logger.debug(f"Failed to get details for branch {branch_name}: {e}")
                    branch_details = branch
                
                # Analyze branch patterns
                branch_type = _classify_branch_type(branch_name)
                if branch_type in branch_stats["branch_patterns"]:
                    branch_stats["branch_patterns"][branch_type] += 1
                
                # Check if branch is stale (no commits in last 90 days)
                commit_date = commit_info.get("commit", {}).get("author", {}).get("date")
                is_stale = False
                if commit_date:
                    try:
                        last_commit_date = datetime.fromisoformat(commit_date.replace("Z", "+00:00"))
                        days_since_commit = (datetime.now(last_commit_date.tzinfo) - last_commit_date).days
                        is_stale = days_since_commit > 90
                        if not is_stale:
                            branch_stats["active_branches"] += 1
                        else:
                            branch_stats["stale_branches"] += 1
                    except Exception:
                        pass
                
                # Get protection information if requested
                protection_info = None
                if include_protection:
                    try:
                        protection = await github_client.get_branch_protection(repo_name, branch_name)
                        protection_info = {
                            "enabled": True,
                            "required_status_checks": protection.get("required_status_checks"),
                            "enforce_admins": protection.get("enforce_admins", {}).get("enabled", False),
                            "required_pull_request_reviews": protection.get("required_pull_request_reviews"),
                            "restrictions": protection.get("restrictions"),
                            "allow_force_pushes": protection.get("allow_force_pushes", {}).get("enabled", False),
                            "allow_deletions": protection.get("allow_deletions", {}).get("enabled", False),
                            "block_creations": protection.get("block_creations", {}).get("enabled", False),
                            "required_conversation_resolution": protection.get("required_conversation_resolution", {}).get("enabled", False)
                        }
                        branch_stats["protected_branches"] += 1
                    except Exception:
                        # Branch is not protected or we don't have permission to see protection
                        protection_info = {"enabled": False}
                
                # Process branch data
                branch_data = {
                    "name": branch_name,
                    "commit": {
                        "sha": commit_info.get("sha"),
                        "url": commit_info.get("url"),
                        "author": commit_info.get("commit", {}).get("author", {}),
                        "committer": commit_info.get("commit", {}).get("committer", {}),
                        "message": commit_info.get("commit", {}).get("message", ""),
                        "date": commit_date
                    },
                    "protected": protection_info.get("enabled", False) if protection_info else False,
                    "protection": protection_info,
                    "is_default": branch_name == default_branch,
                    "is_stale": is_stale,
                    "branch_type": branch_type,
                    "links": {
                        "self": branch_details.get("_links", {}).get("self") if branch_details else None,
                        "html": branch_details.get("_links", {}).get("html") if branch_details else None
                    }
                }
                
                processed_branches.append(branch_data)
            
            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Prepare response data
            response_data = {
                "repository": repo_name,
                "branches": processed_branches,
                "statistics": branch_stats,
                "analysis": {
                    "branch_health": _calculate_branch_health(branch_stats),
                    "protection_coverage": round((branch_stats["protected_branches"] / branch_stats["total_branches"]) * 100, 2) if branch_stats["total_branches"] > 0 else 0,
                    "activity_ratio": round((branch_stats["active_branches"] / branch_stats["total_branches"]) * 100, 2) if branch_stats["total_branches"] > 0 else 0,
                    "stale_ratio": round((branch_stats["stale_branches"] / branch_stats["total_branches"]) * 100, 2) if branch_stats["total_branches"] > 0 else 0
                },
                "query_parameters": {
                    "max_branches": max_branches,
                    "include_protection": include_protection
                }
            }
            
            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                github_url=f"https://api.github.com/repos/{repo_name}/branches",
                total_records=len(processed_branches),
                query_duration_ms=response_time_ms,
                api_version=credentials.api_version
            )
            
            # Log successful operation
            await log_audit_event(
                event_type="github_branch_analysis_completed",
                user=user_identity,
                repository=repo_name,
                branches_count=len(processed_branches),
                protected_branches=branch_stats["protected_branches"],
                response_time_ms=response_time_ms
            )
            
            logger.info(
                "Branch analysis completed",
                extra={
                    "repo_name": repo_name,
                    "branches_count": len(processed_branches),
                    "protected_branches": branch_stats["protected_branches"],
                    "active_branches": branch_stats["active_branches"],
                    "response_time_ms": response_time_ms
                }
            )
            
            return GitHubResponse(
                status="success",
                data=response_data,
                metadata=metadata
            ).dict()
            
    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)
        
        # Log error event
        await log_error_event(
            event_type="github_branch_analysis_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            repository=repo_name,
            response_time_ms=response_time_ms
        )
        
        logger.error(
            "Branch analysis failed",
            extra={
                "repo_name": repo_name,
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )
        
        # Prepare error response
        error_data = {
            "repository": repo_name,
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }
        
        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )
        
        return GitHubResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {"repository": repo_name}
            }]
        ).dict()


def _classify_branch_type(branch_name: str) -> str:
    """Classify branch type based on naming patterns."""
    branch_lower = branch_name.lower()
    
    if any(pattern in branch_lower for pattern in ["feature/", "feat/", "feature-"]):
        return "feature_branches"
    elif any(pattern in branch_lower for pattern in ["hotfix/", "hotfix-", "fix/"]):
        return "hotfix_branches"
    elif any(pattern in branch_lower for pattern in ["release/", "release-", "rel/"]):
        return "release_branches"
    elif branch_lower in ["develop", "development", "dev"]:
        return "develop_branches"
    else:
        return "other_branches"


def _calculate_branch_health(stats: Dict[str, Any]) -> str:
    """Calculate overall branch health score."""
    total_branches = stats["total_branches"]
    if total_branches == 0:
        return "unknown"
    
    protected_ratio = stats["protected_branches"] / total_branches
    active_ratio = stats["active_branches"] / total_branches
    stale_ratio = stats["stale_branches"] / total_branches
    
    # Calculate health score (0-100)
    health_score = (protected_ratio * 40) + (active_ratio * 40) + ((1 - stale_ratio) * 20)
    
    if health_score >= 80:
        return "excellent"
    elif health_score >= 60:
        return "good"
    elif health_score >= 40:
        return "fair"
    else:
        return "needs_improvement"
