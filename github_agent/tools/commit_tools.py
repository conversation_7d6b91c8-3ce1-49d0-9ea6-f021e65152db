"""Commit analysis tools for GitHub Reader Agent."""

import os
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from google.adk.tools import ToolContext

from ..config.settings import get_settings
from ..config.schemas import GitHubResponse, ResponseMetadata
from shared_utils.auth import get_credentials_from_secret_manager, get_user_identity_from_context
from shared_utils.exceptions import ValidationError
from shared_utils.logging import get_logger, log_audit_event, log_error_event
from shared_utils.models import GitHubCredentials
from shared_utils.validation import validate_request_permissions, validate_repository_name
from shared_utils.performance import timed, cached
from .github_client import GitHubClientWrapper

logger = get_logger(__name__)


def _datetime_to_iso(dt: datetime) -> str:
    """Convert datetime to ISO format string."""
    return dt.isoformat() + "Z" if dt.tzinfo is None else dt.isoformat()


@timed("get_commits_with_metadata")
@cached(ttl=300)
async def get_commits_with_metadata(
    repo_name: str,
    branch: str = "main",
    max_commits: int = 100,
    since_days: Optional[int] = None,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Get commits with comprehensive metadata for analysis.
    
    This tool retrieves commit history with detailed metadata including
    author information, file changes, and commit statistics.
    
    Args:
        repo_name: Repository name in format "owner/repo"
        branch: Branch name to get commits from
        max_commits: Maximum number of commits to retrieve
        since_days: Only get commits from last N days
        tool_context: ADK tool context with authentication and session info
        
    Returns:
        Dict containing comprehensive commit data
        
    Raises:
        ValidationError: If repository name is invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting commits retrieval with metadata",
        extra={
            "repo_name": repo_name,
            "branch": branch,
            "max_commits": max_commits,
            "since_days": since_days,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )
    
    start_time = datetime.utcnow()
    
    # Validate input parameters
    validate_repository_name(repo_name)
    
    # Validate permissions
    await validate_request_permissions(tool_context, "repos.read")
    
    try:
        # Get GitHub credentials
        user_identity = await get_user_identity_from_context(tool_context)
        settings = get_settings()
        
        # Try to get GitHub token from environment variables first
        github_token = os.getenv("GITHUB_TOKEN") or os.getenv("GITHUB_PERSONAL_ACCESS_TOKEN")
        
        if github_token:
            credentials = GitHubCredentials(
                token=github_token,
                service_name="github"
            )
        elif tool_context and user_identity and hasattr(user_identity, 'user_id') and user_identity.user_id != 'test_user':
            try:
                credentials = await get_credentials_from_secret_manager(
                    settings.github_credentials_secret,
                    GitHubCredentials
                )
            except Exception as e:
                logger.warning(f"Failed to get credentials from Secret Manager: {e}")
                credentials = GitHubCredentials(
                    token="test_github_token",
                    service_name="github"
                )
        else:
            credentials = GitHubCredentials(
                token=github_token or "test_github_token",
                service_name="github"
            )
        
        async with GitHubClientWrapper(credentials) as github_client:
            # Prepare parameters for commit retrieval
            params = {
                "sha": branch,
                "per_page": min(max_commits, 100)
            }
            
            # Add since parameter if specified
            if since_days:
                since_date = datetime.utcnow() - timedelta(days=since_days)
                params["since"] = since_date.isoformat()
            
            # Get commits
            commits = await github_client.get_commits(repo_name, **params)
            
            # Process commits with detailed metadata
            processed_commits = []
            unique_authors = set()
            total_additions = 0
            total_deletions = 0
            
            for commit in commits:
                commit_data = commit.get("commit", {})
                author_info = commit_data.get("author", {})
                committer_info = commit_data.get("committer", {})
                
                # Extract author information
                author_name = author_info.get("name", "Unknown")
                author_email = author_info.get("email", "")
                unique_authors.add(author_email if author_email else author_name)
                
                # Get commit statistics
                stats = commit.get("stats", {})
                additions = stats.get("additions", 0)
                deletions = stats.get("deletions", 0)
                total_additions += additions
                total_deletions += deletions
                
                processed_commit = {
                    "sha": commit.get("sha"),
                    "message": commit_data.get("message", ""),
                    "author": {
                        "name": author_name,
                        "email": author_email,
                        "date": author_info.get("date")
                    },
                    "committer": {
                        "name": committer_info.get("name", "Unknown"),
                        "email": committer_info.get("email", ""),
                        "date": committer_info.get("date")
                    },
                    "url": commit.get("html_url"),
                    "api_url": commit.get("url"),
                    "stats": {
                        "additions": additions,
                        "deletions": deletions,
                        "total_changes": additions + deletions
                    },
                    "parents": [p.get("sha") for p in commit.get("parents", [])],
                    "verification": commit.get("commit", {}).get("verification", {})
                }
                
                processed_commits.append(processed_commit)
            
            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Prepare analysis data
            analysis_data = {
                "total_commits": len(processed_commits),
                "unique_authors": list(unique_authors),
                "total_authors": len(unique_authors),
                "total_additions": total_additions,
                "total_deletions": total_deletions,
                "total_changes": total_additions + total_deletions,
                "average_changes_per_commit": round((total_additions + total_deletions) / len(processed_commits), 2) if processed_commits else 0,
                "branch_analyzed": branch,
                "time_range": {
                    "since_days": since_days,
                    "oldest_commit": processed_commits[-1].get("author", {}).get("date") if processed_commits else None,
                    "newest_commit": processed_commits[0].get("author", {}).get("date") if processed_commits else None
                }
            }
            
            # Prepare response data
            response_data = {
                "repository": repo_name,
                "commits": processed_commits,
                "analysis": analysis_data
            }
            
            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                github_url=f"https://api.github.com/repos/{repo_name}/commits",
                total_records=len(processed_commits),
                query_duration_ms=response_time_ms,
                api_version=credentials.api_version
            )
            
            # Log successful operation
            await log_audit_event(
                event_type="github_commits_retrieved",
                user=user_identity,
                repository=repo_name,
                commits_count=len(processed_commits),
                response_time_ms=response_time_ms
            )
            
            logger.info(
                "Commits retrieval completed",
                extra={
                    "repo_name": repo_name,
                    "branch": branch,
                    "commits_count": len(processed_commits),
                    "unique_authors": len(unique_authors),
                    "response_time_ms": response_time_ms
                }
            )
            
            return GitHubResponse(
                status="success",
                data=response_data,
                metadata=metadata
            ).dict()
            
    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)
        
        # Log error event
        await log_error_event(
            event_type="github_commits_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            repository=repo_name,
            response_time_ms=response_time_ms
        )
        
        logger.error(
            "Commits retrieval failed",
            extra={
                "repo_name": repo_name,
                "branch": branch,
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )
        
        # Prepare error response
        error_data = {
            "repository": repo_name,
            "branch": branch,
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }
        
        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )
        
        return GitHubResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {"repository": repo_name, "branch": branch}
            }]
        ).dict()


@timed("get_commit_details")
@cached(ttl=600)
async def get_commit_details(
    repo_name: str,
    commit_hash: str,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Get detailed information about a specific commit.

    This tool retrieves comprehensive commit information including
    file changes, statistics, and verification details.

    Args:
        repo_name: Repository name in format "owner/repo"
        commit_hash: The git commit hash to analyze
        tool_context: ADK tool context with authentication and session info

    Returns:
        Dict containing detailed commit information

    Raises:
        ValidationError: If repository name or commit hash is invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting detailed commit retrieval",
        extra={
            "repo_name": repo_name,
            "commit_hash": commit_hash,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )

    start_time = datetime.utcnow()

    # Validate input parameters
    validate_repository_name(repo_name)
    if not commit_hash or len(commit_hash) < 7:
        raise ValidationError("Invalid commit hash")

    # Validate permissions
    await validate_request_permissions(tool_context, "repos.read")

    try:
        # Get GitHub credentials
        user_identity = await get_user_identity_from_context(tool_context)
        settings = get_settings()

        # Try to get GitHub token from environment variables first
        github_token = os.getenv("GITHUB_TOKEN") or os.getenv("GITHUB_PERSONAL_ACCESS_TOKEN")

        if github_token:
            credentials = GitHubCredentials(
                token=github_token,
                service_name="github"
            )
        elif tool_context and user_identity and hasattr(user_identity, 'user_id') and user_identity.user_id != 'test_user':
            try:
                credentials = await get_credentials_from_secret_manager(
                    settings.github_credentials_secret,
                    GitHubCredentials
                )
            except Exception as e:
                logger.warning(f"Failed to get credentials from Secret Manager: {e}")
                credentials = GitHubCredentials(
                    token="test_github_token",
                    service_name="github"
                )
        else:
            credentials = GitHubCredentials(
                token=github_token or "test_github_token",
                service_name="github"
            )

        async with GitHubClientWrapper(credentials) as github_client:
            # Get detailed commit information
            commit = await github_client.get_commit(repo_name, commit_hash)

            # Extract commit information
            commit_data = commit.get("commit", {})
            author_info = commit_data.get("author", {})
            committer_info = commit_data.get("committer", {})
            stats = commit.get("stats", {})

            # Process file changes
            files_changed = []
            for file in commit.get("files", []):
                file_data = {
                    "filename": file.get("filename"),
                    "status": file.get("status"),
                    "additions": file.get("additions", 0),
                    "deletions": file.get("deletions", 0),
                    "changes": file.get("changes", 0),
                    "blob_url": file.get("blob_url"),
                    "raw_url": file.get("raw_url"),
                    "contents_url": file.get("contents_url"),
                    "patch": file.get("patch", "")[:1000] if file.get("patch") else ""  # Limit patch size
                }
                files_changed.append(file_data)

            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)

            # Prepare detailed commit data
            commit_details = {
                "sha": commit.get("sha"),
                "node_id": commit.get("node_id"),
                "url": commit.get("html_url"),
                "api_url": commit.get("url"),
                "message": commit_data.get("message", ""),
                "author": {
                    "name": author_info.get("name"),
                    "email": author_info.get("email"),
                    "date": author_info.get("date")
                },
                "committer": {
                    "name": committer_info.get("name"),
                    "email": committer_info.get("email"),
                    "date": committer_info.get("date")
                },
                "github_author": {
                    "login": commit.get("author", {}).get("login") if commit.get("author") else None,
                    "id": commit.get("author", {}).get("id") if commit.get("author") else None,
                    "avatar_url": commit.get("author", {}).get("avatar_url") if commit.get("author") else None
                },
                "github_committer": {
                    "login": commit.get("committer", {}).get("login") if commit.get("committer") else None,
                    "id": commit.get("committer", {}).get("id") if commit.get("committer") else None,
                    "avatar_url": commit.get("committer", {}).get("avatar_url") if commit.get("committer") else None
                },
                "parents": [
                    {
                        "sha": parent.get("sha"),
                        "url": parent.get("url"),
                        "html_url": parent.get("html_url")
                    }
                    for parent in commit.get("parents", [])
                ],
                "stats": {
                    "total": stats.get("total", 0),
                    "additions": stats.get("additions", 0),
                    "deletions": stats.get("deletions", 0)
                },
                "files": files_changed,
                "verification": commit_data.get("verification", {}),
                "tree": {
                    "sha": commit_data.get("tree", {}).get("sha"),
                    "url": commit_data.get("tree", {}).get("url")
                }
            }

            # Prepare response data
            response_data = {
                "repository": repo_name,
                "commit": commit_details,
                "summary": {
                    "total_files": len(files_changed),
                    "total_additions": stats.get("additions", 0),
                    "total_deletions": stats.get("deletions", 0),
                    "total_changes": stats.get("total", 0),
                    "is_merge_commit": len(commit.get("parents", [])) > 1,
                    "parent_count": len(commit.get("parents", [])),
                    "verified": commit_data.get("verification", {}).get("verified", False)
                }
            }

            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                github_url=f"https://api.github.com/repos/{repo_name}/commits/{commit_hash}",
                total_records=1,
                query_duration_ms=response_time_ms,
                api_version=credentials.api_version
            )

            # Log successful operation
            await log_audit_event(
                event_type="github_commit_details_retrieved",
                user=user_identity,
                repository=repo_name,
                commit_hash=commit_hash,
                response_time_ms=response_time_ms
            )

            logger.info(
                "Commit details retrieval completed",
                extra={
                    "repo_name": repo_name,
                    "commit_hash": commit_hash,
                    "files_changed": len(files_changed),
                    "total_changes": stats.get("total", 0),
                    "response_time_ms": response_time_ms
                }
            )

            return GitHubResponse(
                status="success",
                data=response_data,
                metadata=metadata
            ).dict()

    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)

        # Log error event
        await log_error_event(
            event_type="github_commit_details_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            repository=repo_name,
            commit_hash=commit_hash,
            response_time_ms=response_time_ms
        )

        logger.error(
            "Commit details retrieval failed",
            extra={
                "repo_name": repo_name,
                "commit_hash": commit_hash,
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )

        # Prepare error response
        error_data = {
            "repository": repo_name,
            "commit_hash": commit_hash,
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }

        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )

        return GitHubResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {"repository": repo_name, "commit_hash": commit_hash}
            }]
        ).dict()
