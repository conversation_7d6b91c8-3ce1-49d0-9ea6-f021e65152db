"""Connection validation tools for GitHub Reader Agent."""

import os
from datetime import datetime
from typing import Any, Dict, Optional

from google.adk.tools import ToolContext

from ..config.settings import get_settings
from ..config.schemas import GitHubResponse, ResponseMetadata
from shared_utils.auth import get_github_credentials, get_user_identity_from_context
from shared_utils.exceptions import AuthenticationError, ConnectionError
from shared_utils.logging import get_logger, log_audit_event, log_error_event
from shared_utils.models import GitHubCredentials
from shared_utils.validation import validate_request_permissions
from .github_client import GitHubClientWrapper

logger = get_logger(__name__)


def _datetime_to_iso(dt: datetime) -> str:
    """Convert datetime to ISO format string."""
    return dt.isoformat() + "Z" if dt.tzinfo is None else dt.isoformat()


async def validate_github_connection(
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Validate GitHub API connection and authentication.
    
    This tool tests the connection to GitHub API and validates the authentication
    credentials. It provides comprehensive connection diagnostics and health checks.
    
    Args:
        tool_context: ADK tool context with authentication and session info
        
    Returns:
        Dict containing connection status and diagnostics
        
    Raises:
        AuthenticationError: If authentication fails
        ConnectionError: If connection cannot be established
    """
    logger.info(
        "Starting GitHub connection validation",
        extra={
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )
    
    start_time = datetime.utcnow()
    settings = get_settings()
    
    # Validate permissions
    await validate_request_permissions(tool_context, "read")
    
    try:
        # Get GitHub credentials using the new credential system
        user_identity = await get_user_identity_from_context(tool_context)
        credentials = await get_github_credentials(tool_context)
        
        # Test connection using GitHub client
        async with GitHubClientWrapper(credentials) as github_client:
            connection_result = await github_client.test_connection()
            
            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            if connection_result.get("connected") and connection_result.get("authenticated"):
                # Successful connection
                response_data = {
                    "connection_status": "connected",
                    "authentication_status": "authenticated",
                    "github_user": connection_result.get("user"),
                    "user_id": connection_result.get("user_id"),
                    "api_version": connection_result.get("api_version"),
                    "server_info": {
                        "base_url": "https://api.github.com",
                        "rate_limit_enabled": settings.enable_rate_limiting,
                        "cache_enabled": settings.enable_caching
                    },
                    "diagnostics": {
                        "connection_time_ms": response_time_ms,
                        "credentials_valid": True,
                        "api_accessible": True
                    }
                }
                
                # Create response metadata
                metadata = ResponseMetadata(
                    timestamp=_datetime_to_iso(end_time),
                    github_url="https://api.github.com",
                    total_records=1,
                    query_duration_ms=response_time_ms,
                    api_version=credentials.api_version
                )
                
                # Log successful connection
                await log_audit_event(
                    event_type="github_connection_validated",
                    user=user_identity,
                    github_user=connection_result.get("user"),
                    response_time_ms=response_time_ms
                )
                
                logger.info(
                    "GitHub connection validation successful",
                    extra={
                        "github_user": connection_result.get("user"),
                        "response_time_ms": response_time_ms
                    }
                )
                
                return GitHubResponse(
                    status="success",
                    data=response_data,
                    metadata=metadata
                ).dict()
            
            else:
                # Connection failed
                error_message = connection_result.get("error", "Unknown connection error")
                
                response_data = {
                    "connection_status": "failed",
                    "authentication_status": "failed",
                    "error": {
                        "type": "ConnectionError",
                        "message": error_message
                    },
                    "diagnostics": {
                        "connection_time_ms": response_time_ms,
                        "credentials_valid": False,
                        "api_accessible": False
                    }
                }
                
                metadata = ResponseMetadata(
                    timestamp=_datetime_to_iso(end_time),
                    total_records=0,
                    query_duration_ms=response_time_ms
                )
                
                # Log connection failure
                await log_error_event(
                    event_type="github_connection_failed",
                    error=error_message,
                    user=getattr(user_identity, 'user_id', 'unknown'),
                    response_time_ms=response_time_ms
                )
                
                logger.error(
                    "GitHub connection validation failed",
                    extra={
                        "error": error_message,
                        "response_time_ms": response_time_ms
                    }
                )
                
                return GitHubResponse(
                    status="error",
                    data=response_data,
                    metadata=metadata,
                    errors=[{
                        "code": "ConnectionError",
                        "message": error_message,
                        "details": {}
                    }]
                ).dict()
    
    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)
        
        # Log error event
        await log_error_event(
            event_type="github_connection_validation_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            response_time_ms=response_time_ms
        )
        
        logger.error(
            "GitHub connection validation error",
            extra={
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )
        
        # Prepare error response
        error_data = {
            "connection_status": "error",
            "authentication_status": "error",
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            },
            "diagnostics": {
                "connection_time_ms": response_time_ms,
                "credentials_valid": False,
                "api_accessible": False
            }
        }
        
        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )
        
        return GitHubResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {}
            }]
        ).dict()


async def get_github_server_status(
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Get GitHub API server status and health information.
    
    This tool provides information about GitHub API status, rate limits,
    and service health for monitoring and diagnostics.
    
    Args:
        tool_context: ADK tool context with authentication and session info
        
    Returns:
        Dict containing GitHub API status information
    """
    logger.info("Getting GitHub server status")
    
    start_time = datetime.utcnow()
    
    # Validate permissions
    await validate_request_permissions(tool_context, "read")
    
    try:
        # Get GitHub credentials using the new credential system
        user_identity = await get_user_identity_from_context(tool_context)
        credentials = await get_github_credentials(tool_context)
        settings = get_settings()
        
        async with GitHubClientWrapper(credentials) as github_client:
            # Get user info to check API status
            user_info = await github_client.get_user()
            
            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Prepare status information
            status_data = {
                "api_status": "operational",
                "base_url": "https://api.github.com",
                "api_version": credentials.api_version,
                "authenticated_user": user_info.get("login"),
                "user_type": user_info.get("type"),
                "rate_limits": {
                    "enabled": settings.enable_rate_limiting,
                    "requests_per_hour": settings.github_requests_per_hour,
                    "requests_per_minute": settings.github_requests_per_minute
                },
                "performance": {
                    "response_time_ms": response_time_ms,
                    "caching_enabled": settings.enable_caching,
                    "max_concurrent_requests": settings.max_concurrent_requests
                },
                "features": {
                    "audit_logging": settings.enable_audit_logging,
                    "input_validation": settings.enable_input_validation,
                    "team_analytics": settings.enable_team_analytics
                }
            }
            
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                github_url="https://api.github.com",
                total_records=1,
                query_duration_ms=response_time_ms,
                api_version=credentials.api_version
            )
            
            logger.info(
                "GitHub server status retrieved successfully",
                extra={
                    "api_status": "operational",
                    "response_time_ms": response_time_ms
                }
            )
            
            return GitHubResponse(
                status="success",
                data=status_data,
                metadata=metadata
            ).dict()
    
    except Exception as e:
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)
        
        logger.error(
            "Failed to get GitHub server status",
            extra={
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )
        
        error_data = {
            "api_status": "error",
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }
        
        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )
        
        return GitHubResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {}
            }]
        ).dict()
