"""Enterprise GitHub API client wrapper with rate limiting and caching."""

import asyncio
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urljoin

import aiohttp
from aiohttp import ClientTimeout

from ..config.settings import get_settings
from shared_utils.exceptions import (
    ConnectionError, AuthenticationError, RateLimitError,
    APIError, TimeoutError as AgentTimeoutError
)
from shared_utils.logging import get_logger, log_audit_event, log_error_event
from shared_utils.models import GitHubCredentials, RateLimitInfo
from shared_utils.performance import cached, timed, performance_monitor
from shared_utils.security import RateLimiter

logger = get_logger(__name__)


class GitHubClientWrapper:
    """
    Enterprise GitHub API client wrapper with comprehensive features:
    - Rate limiting and quota management
    - Caching for performance optimization
    - Comprehensive error handling and retry logic
    - Audit logging and security monitoring
    - Connection pooling and timeout management
    """
    
    def __init__(self, credentials: GitHubCredentials):
        self.credentials = credentials
        self.settings = get_settings()
        self.base_url = "https://api.github.com"
        self.session = None
        self.rate_limiter = RateLimiter()
        self._closed = False
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._create_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def _create_session(self) -> None:
        """Create HTTP session with proper configuration."""
        timeout = ClientTimeout(total=self.settings.request_timeout_seconds)
        
        headers = {
            "Authorization": f"token {self.credentials.token}",
            "Accept": "application/vnd.github.v3+json",
            "X-GitHub-Api-Version": self.credentials.api_version,
            "User-Agent": f"GitHub-Agent/{self.settings.environment}"
        }
        
        # Add any additional headers
        headers.update(self.credentials.additional_headers)
        
        connector = aiohttp.TCPConnector(
            limit=self.settings.max_concurrent_requests,
            limit_per_host=self.settings.max_concurrent_requests,
            ttl_dns_cache=300,
            use_dns_cache=True
        )
        
        self.session = aiohttp.ClientSession(
            headers=headers,
            timeout=timeout,
            connector=connector
        )
        
        logger.info(
            "GitHub client session created",
            extra={
                "base_url": self.base_url,
                "api_version": self.credentials.api_version,
                "timeout": self.settings.request_timeout_seconds
            }
        )
    
    async def close(self) -> None:
        """Close the HTTP session."""
        if self.session and not self._closed:
            await self.session.close()
            self._closed = True
            logger.debug("GitHub client session closed")
    
    @timed("github_api_request")
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Make HTTP request to GitHub API with comprehensive error handling.
        
        Args:
            method: HTTP method
            endpoint: API endpoint (relative to base URL)
            params: Query parameters
            data: Request body data
            headers: Additional headers
            
        Returns:
            Dict containing response data
            
        Raises:
            ConnectionError: If connection fails
            AuthenticationError: If authentication fails
            RateLimitError: If rate limit is exceeded
            APIError: If API returns an error
            AgentTimeoutError: If request times out
        """
        if not self.session:
            await self._create_session()
        
        url = urljoin(self.base_url, endpoint.lstrip('/'))
        
        # Check rate limits
        await self._check_rate_limits()
        
        # Prepare request
        request_headers = headers or {}
        
        logger.debug(
            "Making GitHub API request",
            extra={
                "method": method,
                "url": url,
                "params": params
            }
        )
        
        # Retry logic
        last_exception = None
        for attempt in range(self.settings.max_retries + 1):
            try:
                async with self.session.request(
                    method=method,
                    url=url,
                    params=params,
                    json=data,
                    headers=request_headers
                ) as response:
                    # Update rate limit info
                    await self._update_rate_limits(response.headers)
                    
                    # Handle different response codes
                    if response.status == 200:
                        result = await response.json()
                        
                        logger.debug(
                            "GitHub API request successful",
                            extra={
                                "url": url,
                                "status": response.status,
                                "attempt": attempt + 1
                            }
                        )
                        
                        return result
                    
                    elif response.status == 304:
                        # Not modified - return empty dict
                        return {}
                    
                    elif response.status == 401:
                        error_text = await response.text()
                        raise AuthenticationError(
                            "GitHub authentication failed",
                            details={
                                "status_code": response.status,
                                "response": error_text[:500]
                            }
                        )
                    
                    elif response.status == 403:
                        error_text = await response.text()
                        
                        # Check if it's a rate limit error
                        if "rate limit" in error_text.lower():
                            reset_time = response.headers.get("X-RateLimit-Reset")
                            retry_after = int(reset_time) - int(time.time()) if reset_time else 3600
                            
                            raise RateLimitError(
                                "GitHub API rate limit exceeded",
                                retry_after=retry_after
                            )
                        else:
                            raise APIError(
                                "GitHub API access forbidden",
                                status_code=response.status,
                                response_body=error_text[:500],
                                api_name="github"
                            )
                    
                    elif response.status == 404:
                        error_text = await response.text()
                        raise APIError(
                            "GitHub resource not found",
                            status_code=response.status,
                            response_body=error_text[:500],
                            api_name="github"
                        )
                    
                    elif response.status >= 500:
                        # Server error - retry
                        error_text = await response.text()
                        last_exception = APIError(
                            f"GitHub API server error: {response.status}",
                            status_code=response.status,
                            response_body=error_text[:500],
                            api_name="github"
                        )
                        
                        if attempt < self.settings.max_retries:
                            wait_time = 2 ** attempt  # Exponential backoff
                            logger.warning(
                                f"GitHub API server error, retrying in {wait_time}s",
                                extra={
                                    "status": response.status,
                                    "attempt": attempt + 1,
                                    "wait_time": wait_time
                                }
                            )
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            raise last_exception
                    
                    else:
                        error_text = await response.text()
                        raise APIError(
                            f"GitHub API error: {response.status}",
                            status_code=response.status,
                            response_body=error_text[:500],
                            api_name="github"
                        )
            
            except asyncio.TimeoutError:
                last_exception = AgentTimeoutError(
                    "GitHub API request timed out",
                    timeout_seconds=self.settings.request_timeout_seconds
                )
                
                if attempt < self.settings.max_retries:
                    wait_time = 2 ** attempt
                    logger.warning(
                        f"GitHub API timeout, retrying in {wait_time}s",
                        extra={
                            "attempt": attempt + 1,
                            "wait_time": wait_time
                        }
                    )
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    raise last_exception
            
            except aiohttp.ClientError as e:
                last_exception = ConnectionError(
                    f"GitHub API connection error: {str(e)}",
                    service_url=url
                )
                
                if attempt < self.settings.max_retries:
                    wait_time = 2 ** attempt
                    logger.warning(
                        f"GitHub API connection error, retrying in {wait_time}s",
                        extra={
                            "error": str(e),
                            "attempt": attempt + 1,
                            "wait_time": wait_time
                        }
                    )
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    raise last_exception
        
        # If we get here, all retries failed
        if last_exception:
            raise last_exception
        else:
            raise APIError("GitHub API request failed after all retries")
    
    async def _check_rate_limits(self) -> None:
        """Check if we're within rate limits."""
        if not self.settings.enable_rate_limiting:
            return
        
        try:
            rate_limit_info = await self.rate_limiter.check_rate_limit(
                user_id="github_client",
                endpoint="github_api",
                custom_limits={
                    "requests_per_minute": self.settings.github_requests_per_minute,
                    "requests_per_hour": self.settings.github_requests_per_hour
                }
            )
            
            logger.debug(
                "Rate limit check passed",
                extra={
                    "remaining": rate_limit_info.remaining,
                    "reset_time": rate_limit_info.reset_time
                }
            )
            
        except RateLimitError:
            logger.warning("Local rate limit exceeded")
            raise
    
    async def _update_rate_limits(self, headers: Dict[str, str]) -> None:
        """Update rate limit information from response headers."""
        try:
            rate_limit_info = self.rate_limiter._parse_github_rate_limit(headers)
            if rate_limit_info:
                logger.debug(
                    "GitHub rate limit updated",
                    extra={
                        "limit": rate_limit_info.limit,
                        "remaining": rate_limit_info.remaining,
                        "reset_time": rate_limit_info.reset_time
                    }
                )
        except Exception as e:
            logger.debug(f"Failed to parse rate limit headers: {e}")
    
    # Core API methods
    async def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make GET request to GitHub API."""
        return await self._make_request("GET", endpoint, params=params)
    
    async def post(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make POST request to GitHub API."""
        return await self._make_request("POST", endpoint, data=data)
    
    async def put(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make PUT request to GitHub API."""
        return await self._make_request("PUT", endpoint, data=data)
    
    async def delete(self, endpoint: str) -> Dict[str, Any]:
        """Make DELETE request to GitHub API."""
        return await self._make_request("DELETE", endpoint)
    
    # Convenience methods for common operations
    async def get_user(self) -> Dict[str, Any]:
        """Get authenticated user information."""
        return await self.get("/user")
    
    async def get_repository(self, repo_name: str) -> Dict[str, Any]:
        """Get repository information."""
        return await self.get(f"/repos/{repo_name}")
    
    async def get_commits(
        self,
        repo_name: str,
        sha: Optional[str] = None,
        path: Optional[str] = None,
        author: Optional[str] = None,
        since: Optional[str] = None,
        until: Optional[str] = None,
        per_page: int = 30,
        page: int = 1
    ) -> List[Dict[str, Any]]:
        """Get repository commits."""
        params = {
            "per_page": per_page,
            "page": page
        }
        
        if sha:
            params["sha"] = sha
        if path:
            params["path"] = path
        if author:
            params["author"] = author
        if since:
            params["since"] = since
        if until:
            params["until"] = until
        
        return await self.get(f"/repos/{repo_name}/commits", params=params)
    
    async def get_commit(self, repo_name: str, sha: str) -> Dict[str, Any]:
        """Get specific commit information."""
        return await self.get(f"/repos/{repo_name}/commits/{sha}")
    
    async def get_pull_requests(
        self,
        repo_name: str,
        state: str = "open",
        head: Optional[str] = None,
        base: Optional[str] = None,
        sort: str = "created",
        direction: str = "desc",
        per_page: int = 30,
        page: int = 1
    ) -> List[Dict[str, Any]]:
        """Get repository pull requests."""
        params = {
            "state": state,
            "sort": sort,
            "direction": direction,
            "per_page": per_page,
            "page": page
        }
        
        if head:
            params["head"] = head
        if base:
            params["base"] = base
        
        return await self.get(f"/repos/{repo_name}/pulls", params=params)
    
    async def get_pull_request(self, repo_name: str, number: int) -> Dict[str, Any]:
        """Get specific pull request information."""
        return await self.get(f"/repos/{repo_name}/pulls/{number}")
    
    async def get_releases(
        self,
        repo_name: str,
        per_page: int = 30,
        page: int = 1
    ) -> List[Dict[str, Any]]:
        """Get repository releases."""
        params = {
            "per_page": per_page,
            "page": page
        }
        
        return await self.get(f"/repos/{repo_name}/releases", params=params)
    
    async def get_branches(
        self,
        repo_name: str,
        protected: Optional[bool] = None,
        per_page: int = 30,
        page: int = 1
    ) -> List[Dict[str, Any]]:
        """Get repository branches."""
        params = {
            "per_page": per_page,
            "page": page
        }
        
        if protected is not None:
            params["protected"] = str(protected).lower()
        
        return await self.get(f"/repos/{repo_name}/branches", params=params)
    
    async def get_workflows(
        self,
        repo_name: str,
        per_page: int = 30,
        page: int = 1
    ) -> Dict[str, Any]:
        """Get repository workflows."""
        params = {
            "per_page": per_page,
            "page": page
        }
        
        return await self.get(f"/repos/{repo_name}/actions/workflows", params=params)
    
    async def get_workflow_runs(
        self,
        repo_name: str,
        workflow_id: Optional[Union[int, str]] = None,
        actor: Optional[str] = None,
        branch: Optional[str] = None,
        event: Optional[str] = None,
        status: Optional[str] = None,
        per_page: int = 30,
        page: int = 1
    ) -> Dict[str, Any]:
        """Get workflow runs."""
        params = {
            "per_page": per_page,
            "page": page
        }
        
        if actor:
            params["actor"] = actor
        if branch:
            params["branch"] = branch
        if event:
            params["event"] = event
        if status:
            params["status"] = status
        
        if workflow_id:
            endpoint = f"/repos/{repo_name}/actions/workflows/{workflow_id}/runs"
        else:
            endpoint = f"/repos/{repo_name}/actions/runs"
        
        return await self.get(endpoint, params=params)

    async def test_connection(self) -> Dict[str, Any]:
        """Test GitHub API connection and authentication."""
        try:
            user_info = await self.get_user()
            return {
                "connected": True,
                "authenticated": True,
                "user": user_info.get("login"),
                "user_id": user_info.get("id"),
                "api_version": self.credentials.api_version
            }
        except Exception as e:
            return {
                "connected": False,
                "authenticated": False,
                "error": str(e)
            }
