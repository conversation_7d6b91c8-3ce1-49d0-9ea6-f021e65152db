"""Pull request analysis tools for GitHub Reader Agent."""

import os
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from google.adk.tools import ToolContext

from ..config.settings import get_settings
from ..config.schemas import GitHubResponse, ResponseMetadata
from shared_utils.auth import get_credentials_from_secret_manager, get_user_identity_from_context
from shared_utils.exceptions import ValidationError
from shared_utils.logging import get_logger, log_audit_event, log_error_event
from shared_utils.models import GitHubCredentials
from shared_utils.validation import validate_request_permissions, validate_repository_name
from shared_utils.performance import timed, cached
from .github_client import GitHubClientWrapper

logger = get_logger(__name__)


def _datetime_to_iso(dt: datetime) -> str:
    """Convert datetime to ISO format string."""
    return dt.isoformat() + "Z" if dt.tzinfo is None else dt.isoformat()


@timed("get_pull_requests_data")
@cached(ttl=300)
async def get_pull_requests_data(
    repo_name: str,
    state: str = "all",
    max_prs: int = 100,
    include_commits: bool = False,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Get comprehensive pull request data and analysis.
    
    This tool retrieves pull request information with detailed metadata
    including review data, merge information, and optional commit details.
    
    Args:
        repo_name: Repository name in format "owner/repo"
        state: PR state ("open", "closed", "all")
        max_prs: Maximum number of PRs to retrieve
        include_commits: Whether to include commit details for each PR
        tool_context: ADK tool context with authentication and session info
        
    Returns:
        Dict containing comprehensive pull request data
        
    Raises:
        ValidationError: If repository name is invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting pull requests data retrieval",
        extra={
            "repo_name": repo_name,
            "state": state,
            "max_prs": max_prs,
            "include_commits": include_commits,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )
    
    start_time = datetime.utcnow()
    
    # Validate input parameters
    validate_repository_name(repo_name)
    if state not in ["open", "closed", "all"]:
        raise ValidationError(f"Invalid state: {state}. Must be 'open', 'closed', or 'all'")
    
    # Validate permissions
    await validate_request_permissions(tool_context, "repos.read")
    
    try:
        # Get GitHub credentials
        user_identity = await get_user_identity_from_context(tool_context)
        settings = get_settings()
        
        # Try to get GitHub token from environment variables first
        github_token = os.getenv("GITHUB_TOKEN") or os.getenv("GITHUB_PERSONAL_ACCESS_TOKEN")
        
        if github_token:
            credentials = GitHubCredentials(
                token=github_token,
                service_name="github"
            )
        elif tool_context and user_identity and hasattr(user_identity, 'user_id') and user_identity.user_id != 'test_user':
            try:
                credentials = await get_credentials_from_secret_manager(
                    settings.github_credentials_secret,
                    GitHubCredentials
                )
            except Exception as e:
                logger.warning(f"Failed to get credentials from Secret Manager: {e}")
                credentials = GitHubCredentials(
                    token="test_github_token",
                    service_name="github"
                )
        else:
            credentials = GitHubCredentials(
                token=github_token or "test_github_token",
                service_name="github"
            )
        
        async with GitHubClientWrapper(credentials) as github_client:
            # Get pull requests
            prs = await github_client.get_pull_requests(
                repo_name,
                state=state,
                sort="updated",
                direction="desc",
                per_page=min(max_prs, 100)
            )
            
            # Process pull requests with detailed metadata
            processed_prs = []
            pr_stats = {
                "total_prs": len(prs),
                "open_prs": 0,
                "closed_prs": 0,
                "merged_prs": 0,
                "draft_prs": 0,
                "unique_authors": set(),
                "total_additions": 0,
                "total_deletions": 0,
                "total_changed_files": 0,
                "review_stats": {
                    "total_reviews": 0,
                    "approved_reviews": 0,
                    "changes_requested": 0,
                    "commented_reviews": 0
                }
            }
            
            for pr in prs:
                # Basic PR information
                pr_data = {
                    "number": pr.get("number"),
                    "title": pr.get("title"),
                    "body": pr.get("body", ""),
                    "state": pr.get("state"),
                    "draft": pr.get("draft", False),
                    "merged": pr.get("merged", False),
                    "mergeable": pr.get("mergeable"),
                    "mergeable_state": pr.get("mergeable_state"),
                    "url": pr.get("html_url"),
                    "api_url": pr.get("url"),
                    "created_at": pr.get("created_at"),
                    "updated_at": pr.get("updated_at"),
                    "closed_at": pr.get("closed_at"),
                    "merged_at": pr.get("merged_at"),
                    "author": {
                        "login": pr.get("user", {}).get("login"),
                        "id": pr.get("user", {}).get("id"),
                        "type": pr.get("user", {}).get("type")
                    },
                    "assignees": [
                        {
                            "login": assignee.get("login"),
                            "id": assignee.get("id")
                        }
                        for assignee in pr.get("assignees", [])
                    ],
                    "labels": [
                        {
                            "name": label.get("name"),
                            "color": label.get("color"),
                            "description": label.get("description")
                        }
                        for label in pr.get("labels", [])
                    ],
                    "milestone": {
                        "title": pr.get("milestone", {}).get("title"),
                        "number": pr.get("milestone", {}).get("number"),
                        "state": pr.get("milestone", {}).get("state")
                    } if pr.get("milestone") else None,
                    "head": {
                        "ref": pr.get("head", {}).get("ref"),
                        "sha": pr.get("head", {}).get("sha"),
                        "repo": pr.get("head", {}).get("repo", {}).get("full_name") if pr.get("head", {}).get("repo") else None
                    },
                    "base": {
                        "ref": pr.get("base", {}).get("ref"),
                        "sha": pr.get("base", {}).get("sha"),
                        "repo": pr.get("base", {}).get("repo", {}).get("full_name") if pr.get("base", {}).get("repo") else None
                    },
                    "stats": {
                        "additions": pr.get("additions", 0),
                        "deletions": pr.get("deletions", 0),
                        "changed_files": pr.get("changed_files", 0),
                        "commits": pr.get("commits", 0),
                        "comments": pr.get("comments", 0),
                        "review_comments": pr.get("review_comments", 0)
                    }
                }
                
                # Update statistics
                if pr.get("state") == "open":
                    pr_stats["open_prs"] += 1
                else:
                    pr_stats["closed_prs"] += 1
                
                if pr.get("merged"):
                    pr_stats["merged_prs"] += 1
                
                if pr.get("draft"):
                    pr_stats["draft_prs"] += 1
                
                author_login = pr.get("user", {}).get("login")
                if author_login:
                    pr_stats["unique_authors"].add(author_login)
                
                pr_stats["total_additions"] += pr.get("additions", 0)
                pr_stats["total_deletions"] += pr.get("deletions", 0)
                pr_stats["total_changed_files"] += pr.get("changed_files", 0)
                
                # Get review information
                try:
                    reviews = await github_client.get_pull_request_reviews(repo_name, pr.get("number"))
                    pr_data["reviews"] = []
                    
                    for review in reviews:
                        review_data = {
                            "id": review.get("id"),
                            "state": review.get("state"),
                            "body": review.get("body", ""),
                            "submitted_at": review.get("submitted_at"),
                            "user": {
                                "login": review.get("user", {}).get("login"),
                                "id": review.get("user", {}).get("id")
                            }
                        }
                        pr_data["reviews"].append(review_data)
                        
                        # Update review statistics
                        pr_stats["review_stats"]["total_reviews"] += 1
                        review_state = review.get("state", "").lower()
                        if review_state == "approved":
                            pr_stats["review_stats"]["approved_reviews"] += 1
                        elif review_state == "changes_requested":
                            pr_stats["review_stats"]["changes_requested"] += 1
                        elif review_state == "commented":
                            pr_stats["review_stats"]["commented_reviews"] += 1
                            
                except Exception as e:
                    logger.debug(f"Failed to get reviews for PR #{pr.get('number')}: {e}")
                    pr_data["reviews"] = []
                
                # Get commits if requested
                if include_commits:
                    try:
                        commits = await github_client.get_pull_request_commits(repo_name, pr.get("number"))
                        pr_data["commits"] = [
                            {
                                "sha": commit.get("sha"),
                                "message": commit.get("commit", {}).get("message", ""),
                                "author": commit.get("commit", {}).get("author", {}),
                                "url": commit.get("html_url")
                            }
                            for commit in commits
                        ]
                    except Exception as e:
                        logger.debug(f"Failed to get commits for PR #{pr.get('number')}: {e}")
                        pr_data["commits"] = []
                
                processed_prs.append(pr_data)
            
            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Finalize statistics
            pr_stats["unique_authors"] = list(pr_stats["unique_authors"])
            pr_stats["total_unique_authors"] = len(pr_stats["unique_authors"])
            pr_stats["merge_rate"] = round((pr_stats["merged_prs"] / pr_stats["closed_prs"]) * 100, 2) if pr_stats["closed_prs"] > 0 else 0
            pr_stats["average_additions_per_pr"] = round(pr_stats["total_additions"] / pr_stats["total_prs"], 2) if pr_stats["total_prs"] > 0 else 0
            pr_stats["average_deletions_per_pr"] = round(pr_stats["total_deletions"] / pr_stats["total_prs"], 2) if pr_stats["total_prs"] > 0 else 0
            pr_stats["average_files_per_pr"] = round(pr_stats["total_changed_files"] / pr_stats["total_prs"], 2) if pr_stats["total_prs"] > 0 else 0
            
            # Prepare response data
            response_data = {
                "repository": repo_name,
                "pull_requests": processed_prs,
                "statistics": pr_stats,
                "query_parameters": {
                    "state": state,
                    "max_prs": max_prs,
                    "include_commits": include_commits
                }
            }
            
            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                github_url=f"https://api.github.com/repos/{repo_name}/pulls",
                total_records=len(processed_prs),
                query_duration_ms=response_time_ms,
                api_version=credentials.api_version
            )
            
            # Log successful operation
            await log_audit_event(
                event_type="github_pull_requests_retrieved",
                user=user_identity,
                repository=repo_name,
                prs_count=len(processed_prs),
                response_time_ms=response_time_ms
            )
            
            logger.info(
                "Pull requests data retrieval completed",
                extra={
                    "repo_name": repo_name,
                    "state": state,
                    "prs_count": len(processed_prs),
                    "merged_prs": pr_stats["merged_prs"],
                    "response_time_ms": response_time_ms
                }
            )
            
            return GitHubResponse(
                status="success",
                data=response_data,
                metadata=metadata
            ).dict()
            
    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)
        
        # Log error event
        await log_error_event(
            event_type="github_pull_requests_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            repository=repo_name,
            response_time_ms=response_time_ms
        )
        
        logger.error(
            "Pull requests data retrieval failed",
            extra={
                "repo_name": repo_name,
                "state": state,
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )
        
        # Prepare error response
        error_data = {
            "repository": repo_name,
            "state": state,
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }
        
        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )
        
        return GitHubResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {"repository": repo_name, "state": state}
            }]
        ).dict()
