"""Release and tag analysis tools for GitHub Reader Agent."""

import os
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from google.adk.tools import ToolContext

from ..config.settings import get_settings
from ..config.schemas import GitHubResponse, ResponseMetadata
from shared_utils.auth import get_credentials_from_secret_manager, get_user_identity_from_context
from shared_utils.exceptions import ValidationError
from shared_utils.logging import get_logger, log_audit_event, log_error_event
from shared_utils.models import GitHubCredentials
from shared_utils.validation import validate_request_permissions, validate_repository_name
from shared_utils.performance import timed, cached
from .github_client import GitHubClientWrapper

logger = get_logger(__name__)


def _datetime_to_iso(dt: datetime) -> str:
    """Convert datetime to ISO format string."""
    return dt.isoformat() + "Z" if dt.tzinfo is None else dt.isoformat()


@timed("get_releases_and_tags")
@cached(ttl=600)
async def get_releases_and_tags(
    repo_name: str,
    max_releases: int = 50,
    include_drafts: bool = False,
    include_prereleases: bool = True,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Get comprehensive release and tag information.
    
    This tool retrieves release information including assets, tag data,
    and deployment patterns for analysis.
    
    Args:
        repo_name: Repository name in format "owner/repo"
        max_releases: Maximum number of releases to retrieve
        include_drafts: Whether to include draft releases
        include_prereleases: Whether to include pre-releases
        tool_context: ADK tool context with authentication and session info
        
    Returns:
        Dict containing comprehensive release and tag data
        
    Raises:
        ValidationError: If repository name is invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting releases and tags retrieval",
        extra={
            "repo_name": repo_name,
            "max_releases": max_releases,
            "include_drafts": include_drafts,
            "include_prereleases": include_prereleases,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )
    
    start_time = datetime.utcnow()
    
    # Validate input parameters
    validate_repository_name(repo_name)
    
    # Validate permissions
    await validate_request_permissions(tool_context, "repos.read")
    
    try:
        # Get GitHub credentials
        user_identity = await get_user_identity_from_context(tool_context)
        settings = get_settings()
        
        # Try to get GitHub token from environment variables first
        github_token = os.getenv("GITHUB_TOKEN") or os.getenv("GITHUB_PERSONAL_ACCESS_TOKEN")
        
        if github_token:
            credentials = GitHubCredentials(
                token=github_token,
                service_name="github"
            )
        elif tool_context and user_identity and hasattr(user_identity, 'user_id') and user_identity.user_id != 'test_user':
            try:
                credentials = await get_credentials_from_secret_manager(
                    settings.github_credentials_secret,
                    GitHubCredentials
                )
            except Exception as e:
                logger.warning(f"Failed to get credentials from Secret Manager: {e}")
                credentials = GitHubCredentials(
                    token="test_github_token",
                    service_name="github"
                )
        else:
            credentials = GitHubCredentials(
                token=github_token or "test_github_token",
                service_name="github"
            )
        
        async with GitHubClientWrapper(credentials) as github_client:
            # Get releases
            releases = await github_client.get_releases(repo_name, per_page=min(max_releases, 100))
            
            # Get tags for additional information
            tags = await github_client.get_tags(repo_name, per_page=100)
            
            # Process releases
            processed_releases = []
            release_stats = {
                "total_releases": 0,
                "draft_releases": 0,
                "prereleases": 0,
                "published_releases": 0,
                "total_assets": 0,
                "total_downloads": 0,
                "unique_authors": set(),
                "release_frequency": {
                    "days_between_releases": [],
                    "average_days": 0
                }
            }
            
            for release in releases:
                # Filter based on preferences
                if not include_drafts and release.get("draft", False):
                    continue
                if not include_prereleases and release.get("prerelease", False):
                    continue
                
                # Process assets
                assets = []
                total_asset_downloads = 0
                for asset in release.get("assets", []):
                    asset_data = {
                        "id": asset.get("id"),
                        "name": asset.get("name"),
                        "label": asset.get("label"),
                        "content_type": asset.get("content_type"),
                        "size": asset.get("size"),
                        "download_count": asset.get("download_count", 0),
                        "created_at": asset.get("created_at"),
                        "updated_at": asset.get("updated_at"),
                        "browser_download_url": asset.get("browser_download_url"),
                        "uploader": {
                            "login": asset.get("uploader", {}).get("login"),
                            "id": asset.get("uploader", {}).get("id")
                        } if asset.get("uploader") else None
                    }
                    assets.append(asset_data)
                    total_asset_downloads += asset.get("download_count", 0)
                
                # Process release data
                release_data = {
                    "id": release.get("id"),
                    "tag_name": release.get("tag_name"),
                    "target_commitish": release.get("target_commitish"),
                    "name": release.get("name"),
                    "body": release.get("body", ""),
                    "draft": release.get("draft", False),
                    "prerelease": release.get("prerelease", False),
                    "created_at": release.get("created_at"),
                    "published_at": release.get("published_at"),
                    "url": release.get("html_url"),
                    "api_url": release.get("url"),
                    "tarball_url": release.get("tarball_url"),
                    "zipball_url": release.get("zipball_url"),
                    "author": {
                        "login": release.get("author", {}).get("login"),
                        "id": release.get("author", {}).get("id"),
                        "avatar_url": release.get("author", {}).get("avatar_url")
                    } if release.get("author") else None,
                    "assets": assets,
                    "assets_count": len(assets),
                    "total_downloads": total_asset_downloads
                }
                
                processed_releases.append(release_data)
                
                # Update statistics
                release_stats["total_releases"] += 1
                if release.get("draft"):
                    release_stats["draft_releases"] += 1
                elif release.get("prerelease"):
                    release_stats["prereleases"] += 1
                else:
                    release_stats["published_releases"] += 1
                
                release_stats["total_assets"] += len(assets)
                release_stats["total_downloads"] += total_asset_downloads
                
                author_login = release.get("author", {}).get("login")
                if author_login:
                    release_stats["unique_authors"].add(author_login)
            
            # Calculate release frequency
            if len(processed_releases) >= 2:
                for i in range(len(processed_releases) - 1):
                    current_date = datetime.fromisoformat(processed_releases[i]["published_at"].replace("Z", "+00:00")) if processed_releases[i]["published_at"] else None
                    next_date = datetime.fromisoformat(processed_releases[i + 1]["published_at"].replace("Z", "+00:00")) if processed_releases[i + 1]["published_at"] else None
                    
                    if current_date and next_date:
                        days_diff = (current_date - next_date).days
                        release_stats["release_frequency"]["days_between_releases"].append(days_diff)
                
                if release_stats["release_frequency"]["days_between_releases"]:
                    release_stats["release_frequency"]["average_days"] = round(
                        sum(release_stats["release_frequency"]["days_between_releases"]) / 
                        len(release_stats["release_frequency"]["days_between_releases"]), 2
                    )
            
            # Process tags
            processed_tags = []
            for tag in tags:
                tag_data = {
                    "name": tag.get("name"),
                    "commit": {
                        "sha": tag.get("commit", {}).get("sha"),
                        "url": tag.get("commit", {}).get("url")
                    },
                    "zipball_url": tag.get("zipball_url"),
                    "tarball_url": tag.get("tarball_url"),
                    "node_id": tag.get("node_id")
                }
                processed_tags.append(tag_data)
            
            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Finalize statistics
            release_stats["unique_authors"] = list(release_stats["unique_authors"])
            release_stats["total_unique_authors"] = len(release_stats["unique_authors"])
            
            # Prepare response data
            response_data = {
                "repository": repo_name,
                "releases": processed_releases,
                "tags": processed_tags,
                "statistics": release_stats,
                "query_parameters": {
                    "max_releases": max_releases,
                    "include_drafts": include_drafts,
                    "include_prereleases": include_prereleases
                }
            }
            
            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                github_url=f"https://api.github.com/repos/{repo_name}/releases",
                total_records=len(processed_releases),
                query_duration_ms=response_time_ms,
                api_version=credentials.api_version
            )
            
            # Log successful operation
            await log_audit_event(
                event_type="github_releases_retrieved",
                user=user_identity,
                repository=repo_name,
                releases_count=len(processed_releases),
                tags_count=len(processed_tags),
                response_time_ms=response_time_ms
            )
            
            logger.info(
                "Releases and tags retrieval completed",
                extra={
                    "repo_name": repo_name,
                    "releases_count": len(processed_releases),
                    "tags_count": len(processed_tags),
                    "total_downloads": release_stats["total_downloads"],
                    "response_time_ms": response_time_ms
                }
            )
            
            return GitHubResponse(
                status="success",
                data=response_data,
                metadata=metadata
            ).dict()
            
    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)
        
        # Log error event
        await log_error_event(
            event_type="github_releases_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            repository=repo_name,
            response_time_ms=response_time_ms
        )
        
        logger.error(
            "Releases and tags retrieval failed",
            extra={
                "repo_name": repo_name,
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )
        
        # Prepare error response
        error_data = {
            "repository": repo_name,
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }
        
        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )
        
        return GitHubResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {"repository": repo_name}
            }]
        ).dict()
