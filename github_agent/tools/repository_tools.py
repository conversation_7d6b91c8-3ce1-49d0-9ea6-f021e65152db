"""Repository information and metrics tools for GitHub Reader Agent."""

import os
from datetime import datetime
from typing import Any, Dict, Optional

from google.adk.tools import ToolContext

from ..config.settings import get_settings
from ..config.schemas import GitHubResponse, ResponseMetadata, RepositoryMetrics
from shared_utils.auth import get_credentials_from_secret_manager, get_user_identity_from_context
from shared_utils.exceptions import ValidationError
from shared_utils.logging import get_logger, log_audit_event, log_error_event
from shared_utils.models import GitHubCredentials
from shared_utils.validation import validate_request_permissions, validate_repository_name
from shared_utils.performance import timed, cached
from .github_client import GitHubClientWrapper

logger = get_logger(__name__)


def _datetime_to_iso(dt: datetime) -> str:
    """Convert datetime to ISO format string."""
    return dt.isoformat() + "Z" if dt.tzinfo is None else dt.isoformat()


@timed("get_repository_info")
@cached(ttl=300)
async def get_repository_info(
    repo_name: str,
    include_topics: bool = True,
    include_languages: bool = True,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Get comprehensive repository information and metadata.
    
    This tool retrieves detailed information about a GitHub repository including
    basic metadata, statistics, configuration, and optional topics and languages.
    Essential for understanding repository structure and team organization.
    
    Args:
        repo_name: Repository name in format "owner/repo"
        include_topics: Whether to include repository topics
        include_languages: Whether to include programming languages breakdown
        tool_context: ADK tool context with authentication and session info
        
    Returns:
        Dict containing comprehensive repository information
        
    Raises:
        ValidationError: If repository name is invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting repository info retrieval",
        extra={
            "repo_name": repo_name,
            "include_topics": include_topics,
            "include_languages": include_languages,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )
    
    start_time = datetime.utcnow()
    
    # Validate input parameters
    validate_repository_name(repo_name)
    
    # Validate permissions
    await validate_request_permissions(tool_context, "repos.read")
    
    try:
        # Get GitHub credentials
        user_identity = await get_user_identity_from_context(tool_context)
        settings = get_settings()

        # Try to get GitHub token from environment variables first
        github_token = os.getenv("GITHUB_TOKEN") or os.getenv("GITHUB_PERSONAL_ACCESS_TOKEN")

        if github_token:
            credentials = GitHubCredentials(
                token=github_token,
                service_name="github"
            )
        elif tool_context and user_identity and hasattr(user_identity, 'user_id') and user_identity.user_id != 'test_user':
            try:
                credentials = await get_credentials_from_secret_manager(
                    settings.github_credentials_secret,
                    GitHubCredentials
                )
            except Exception as e:
                logger.warning(f"Failed to get credentials from Secret Manager: {e}")
                credentials = GitHubCredentials(
                    token="test_github_token",
                    service_name="github"
                )
        else:
            credentials = GitHubCredentials(
                token=github_token or "test_github_token",
                service_name="github"
            )
        
        async with GitHubClientWrapper(credentials) as github_client:
            # Get basic repository information
            repo_info = await github_client.get_repository(repo_name)
            
            # Prepare additional data requests
            additional_data = {}
            
            # Get topics if requested
            if include_topics:
                try:
                    topics_response = await github_client.get(
                        f"/repos/{repo_name}/topics",
                        headers={"Accept": "application/vnd.github.mercy-preview+json"}
                    )
                    additional_data["topics"] = topics_response.get("names", [])
                except Exception as e:
                    logger.debug(f"Failed to get topics: {e}")
                    additional_data["topics"] = []
            
            # Get languages if requested
            if include_languages:
                try:
                    languages = await github_client.get(f"/repos/{repo_name}/languages")
                    additional_data["languages"] = languages
                    
                    # Calculate language percentages
                    total_bytes = sum(languages.values()) if languages else 0
                    if total_bytes > 0:
                        language_percentages = {
                            lang: round((bytes_count / total_bytes) * 100, 2)
                            for lang, bytes_count in languages.items()
                        }
                        additional_data["language_percentages"] = language_percentages
                    else:
                        additional_data["language_percentages"] = {}
                except Exception as e:
                    logger.debug(f"Failed to get languages: {e}")
                    additional_data["languages"] = {}
                    additional_data["language_percentages"] = {}
            
            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Extract and format repository information
            repository_data = {
                "basic_info": {
                    "id": repo_info.get("id"),
                    "name": repo_info.get("name"),
                    "full_name": repo_info.get("full_name"),
                    "description": repo_info.get("description"),
                    "homepage": repo_info.get("homepage"),
                    "url": repo_info.get("html_url"),
                    "clone_url": repo_info.get("clone_url"),
                    "ssh_url": repo_info.get("ssh_url"),
                    "default_branch": repo_info.get("default_branch"),
                    "visibility": repo_info.get("visibility", "public"),
                    "private": repo_info.get("private", False),
                    "archived": repo_info.get("archived", False),
                    "disabled": repo_info.get("disabled", False),
                    "fork": repo_info.get("fork", False)
                },
                "owner_info": {
                    "login": repo_info.get("owner", {}).get("login"),
                    "id": repo_info.get("owner", {}).get("id"),
                    "type": repo_info.get("owner", {}).get("type"),
                    "url": repo_info.get("owner", {}).get("html_url")
                },
                "statistics": {
                    "size_kb": repo_info.get("size", 0),
                    "stargazers_count": repo_info.get("stargazers_count", 0),
                    "watchers_count": repo_info.get("watchers_count", 0),
                    "forks_count": repo_info.get("forks_count", 0),
                    "open_issues_count": repo_info.get("open_issues_count", 0),
                    "subscribers_count": repo_info.get("subscribers_count", 0),
                    "network_count": repo_info.get("network_count", 0)
                },
                "timestamps": {
                    "created_at": repo_info.get("created_at"),
                    "updated_at": repo_info.get("updated_at"),
                    "pushed_at": repo_info.get("pushed_at")
                },
                "configuration": {
                    "has_issues": repo_info.get("has_issues", False),
                    "has_projects": repo_info.get("has_projects", False),
                    "has_wiki": repo_info.get("has_wiki", False),
                    "has_pages": repo_info.get("has_pages", False),
                    "has_downloads": repo_info.get("has_downloads", False),
                    "has_discussions": repo_info.get("has_discussions", False),
                    "allow_forking": repo_info.get("allow_forking", True),
                    "allow_merge_commit": repo_info.get("allow_merge_commit", True),
                    "allow_squash_merge": repo_info.get("allow_squash_merge", True),
                    "allow_rebase_merge": repo_info.get("allow_rebase_merge", True),
                    "delete_branch_on_merge": repo_info.get("delete_branch_on_merge", False)
                },
                "license": {
                    "key": repo_info.get("license", {}).get("key") if repo_info.get("license") else None,
                    "name": repo_info.get("license", {}).get("name") if repo_info.get("license") else None,
                    "spdx_id": repo_info.get("license", {}).get("spdx_id") if repo_info.get("license") else None
                }
            }
            
            # Add additional data if requested
            if include_topics:
                repository_data["topics"] = additional_data.get("topics", [])
            
            if include_languages:
                repository_data["languages"] = {
                    "breakdown": additional_data.get("languages", {}),
                    "percentages": additional_data.get("language_percentages", {}),
                    "primary_language": repo_info.get("language")
                }
            
            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                github_url=f"https://api.github.com/repos/{repo_name}",
                total_records=1,
                query_duration_ms=response_time_ms,
                api_version=credentials.api_version
            )
            
            # Log successful operation
            await log_audit_event(
                event_type="github_repository_info_retrieved",
                user=user_identity,
                repository=repo_name,
                response_time_ms=response_time_ms
            )
            
            logger.info(
                "Repository info retrieval completed",
                extra={
                    "repo_name": repo_name,
                    "repo_id": repo_info.get("id"),
                    "stars": repo_info.get("stargazers_count", 0),
                    "forks": repo_info.get("forks_count", 0),
                    "response_time_ms": response_time_ms
                }
            )
            
            return GitHubResponse(
                status="success",
                data=repository_data,
                metadata=metadata
            ).dict()
            
    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)
        
        # Log error event
        await log_error_event(
            event_type="github_repository_info_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            repository=repo_name,
            response_time_ms=response_time_ms
        )
        
        logger.error(
            "Repository info retrieval failed",
            extra={
                "repo_name": repo_name,
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )
        
        # Prepare error response
        error_data = {
            "repository": repo_name,
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }
        
        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )
        
        return GitHubResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {"repository": repo_name}
            }]
        ).dict()


@timed("get_repository_metrics")
@cached(ttl=600)  # Cache for 10 minutes since metrics change less frequently
async def get_repository_metrics(
    repo_name: str,
    include_contributor_stats: bool = True,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Get comprehensive repository metrics and analytics.

    This tool calculates detailed metrics about repository activity, contributors,
    and overall health. Essential for team productivity analysis and DORA metrics.

    Args:
        repo_name: Repository name in format "owner/repo"
        include_contributor_stats: Whether to include detailed contributor statistics
        tool_context: ADK tool context with authentication and session info

    Returns:
        Dict containing comprehensive repository metrics

    Raises:
        ValidationError: If repository name is invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting repository metrics calculation",
        extra={
            "repo_name": repo_name,
            "include_contributor_stats": include_contributor_stats,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )

    start_time = datetime.utcnow()

    # Validate input parameters
    validate_repository_name(repo_name)

    # Validate permissions
    await validate_request_permissions(tool_context, "repos.read")

    try:
        # Get GitHub credentials
        user_identity = await get_user_identity_from_context(tool_context)
        settings = get_settings()

        # Try to get GitHub token from environment variables first
        github_token = os.getenv("GITHUB_TOKEN") or os.getenv("GITHUB_PERSONAL_ACCESS_TOKEN")

        if github_token:
            credentials = GitHubCredentials(
                token=github_token,
                service_name="github"
            )
        elif tool_context and user_identity and hasattr(user_identity, 'user_id') and user_identity.user_id != 'test_user':
            try:
                credentials = await get_credentials_from_secret_manager(
                    settings.github_credentials_secret,
                    GitHubCredentials
                )
            except Exception as e:
                logger.warning(f"Failed to get credentials from Secret Manager: {e}")
                credentials = GitHubCredentials(
                    token="test_github_token",
                    service_name="github"
                )
        else:
            credentials = GitHubCredentials(
                token=github_token or "test_github_token",
                service_name="github"
            )

        async with GitHubClientWrapper(credentials) as github_client:
            # Get basic repository information
            repo_info = await github_client.get_repository(repo_name)

            # Get additional metrics data
            metrics_data = {}

            # Get contributor statistics if requested
            if include_contributor_stats:
                try:
                    contributors = await github_client.get(f"/repos/{repo_name}/contributors")
                    metrics_data["contributors"] = contributors
                    metrics_data["total_contributors"] = len(contributors)

                    # Calculate contributor activity
                    if contributors:
                        total_contributions = sum(c.get("contributions", 0) for c in contributors)
                        top_contributor = max(contributors, key=lambda x: x.get("contributions", 0))
                        metrics_data["total_contributions"] = total_contributions
                        metrics_data["top_contributor"] = {
                            "login": top_contributor.get("login"),
                            "contributions": top_contributor.get("contributions", 0),
                            "percentage": round((top_contributor.get("contributions", 0) / total_contributions) * 100, 2) if total_contributions > 0 else 0
                        }
                except Exception as e:
                    logger.debug(f"Failed to get contributor stats: {e}")
                    metrics_data["contributors"] = []
                    metrics_data["total_contributors"] = 0
                    metrics_data["total_contributions"] = 0

            # Get recent activity metrics
            try:
                # Get recent commits (last 30 days)
                since_date = (datetime.utcnow() - datetime.timedelta(days=30)).isoformat()
                recent_commits = await github_client.get_commits(
                    repo_name,
                    since=since_date,
                    per_page=100
                )
                metrics_data["recent_commits_30d"] = len(recent_commits)

                # Calculate commit frequency
                if recent_commits:
                    metrics_data["avg_commits_per_day"] = round(len(recent_commits) / 30, 2)

                    # Get unique contributors in last 30 days
                    recent_contributors = set()
                    for commit in recent_commits:
                        author = commit.get("author")
                        if author and author.get("login"):
                            recent_contributors.add(author.get("login"))
                    metrics_data["active_contributors_30d"] = len(recent_contributors)
                else:
                    metrics_data["avg_commits_per_day"] = 0
                    metrics_data["active_contributors_30d"] = 0

            except Exception as e:
                logger.debug(f"Failed to get recent activity: {e}")
                metrics_data["recent_commits_30d"] = 0
                metrics_data["avg_commits_per_day"] = 0
                metrics_data["active_contributors_30d"] = 0

            # Get pull request metrics
            try:
                # Get recent PRs
                recent_prs = await github_client.get_pull_requests(
                    repo_name,
                    state="all",
                    sort="updated",
                    per_page=100
                )

                open_prs = [pr for pr in recent_prs if pr.get("state") == "open"]
                closed_prs = [pr for pr in recent_prs if pr.get("state") == "closed"]
                merged_prs = [pr for pr in closed_prs if pr.get("merged_at")]

                metrics_data["pull_requests"] = {
                    "total_recent": len(recent_prs),
                    "open": len(open_prs),
                    "closed": len(closed_prs),
                    "merged": len(merged_prs),
                    "merge_rate": round((len(merged_prs) / len(closed_prs)) * 100, 2) if closed_prs else 0
                }

            except Exception as e:
                logger.debug(f"Failed to get PR metrics: {e}")
                metrics_data["pull_requests"] = {
                    "total_recent": 0,
                    "open": 0,
                    "closed": 0,
                    "merged": 0,
                    "merge_rate": 0
                }

            # Get release metrics
            try:
                releases = await github_client.get_releases(repo_name, per_page=50)
                metrics_data["releases"] = {
                    "total": len(releases),
                    "latest": releases[0] if releases else None
                }

                # Calculate release frequency
                if len(releases) >= 2:
                    latest_release = datetime.fromisoformat(releases[0].get("created_at", "").replace("Z", "+00:00"))
                    previous_release = datetime.fromisoformat(releases[1].get("created_at", "").replace("Z", "+00:00"))
                    days_between = (latest_release - previous_release).days
                    metrics_data["releases"]["avg_days_between_releases"] = days_between
                else:
                    metrics_data["releases"]["avg_days_between_releases"] = None

            except Exception as e:
                logger.debug(f"Failed to get release metrics: {e}")
                metrics_data["releases"] = {
                    "total": 0,
                    "latest": None,
                    "avg_days_between_releases": None
                }

            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)

            # Compile comprehensive metrics
            repository_metrics = RepositoryMetrics(
                total_commits=metrics_data.get("total_contributions", 0),
                total_contributors=metrics_data.get("total_contributors", 0),
                total_pull_requests=metrics_data.get("pull_requests", {}).get("total_recent", 0),
                total_releases=metrics_data.get("releases", {}).get("total", 0),
                total_branches=0,  # Would need separate API call
                total_tags=0,      # Would need separate API call
                open_issues=repo_info.get("open_issues_count", 0),
                closed_issues=0,   # Would need separate API call
                forks_count=repo_info.get("forks_count", 0),
                stars_count=repo_info.get("stargazers_count", 0),
                watchers_count=repo_info.get("watchers_count", 0),
                size_kb=repo_info.get("size", 0),
                default_branch=repo_info.get("default_branch", "main"),
                language=repo_info.get("language"),
                created_at=repo_info.get("created_at", ""),
                updated_at=repo_info.get("updated_at", ""),
                pushed_at=repo_info.get("pushed_at", "")
            )

            # Prepare response data
            response_data = {
                "repository": repo_name,
                "metrics": repository_metrics.dict(),
                "activity_metrics": {
                    "recent_commits_30d": metrics_data.get("recent_commits_30d", 0),
                    "avg_commits_per_day": metrics_data.get("avg_commits_per_day", 0),
                    "active_contributors_30d": metrics_data.get("active_contributors_30d", 0),
                    "pull_requests": metrics_data.get("pull_requests", {}),
                    "releases": metrics_data.get("releases", {})
                },
                "health_score": _calculate_repository_health_score(repository_metrics, metrics_data)
            }

            if include_contributor_stats and "top_contributor" in metrics_data:
                response_data["top_contributor"] = metrics_data["top_contributor"]

            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                github_url=f"https://api.github.com/repos/{repo_name}",
                total_records=1,
                query_duration_ms=response_time_ms,
                api_version=credentials.api_version
            )

            # Log successful operation
            await log_audit_event(
                event_type="github_repository_metrics_calculated",
                user=user_identity,
                repository=repo_name,
                metrics_calculated=True,
                response_time_ms=response_time_ms
            )

            logger.info(
                "Repository metrics calculation completed",
                extra={
                    "repo_name": repo_name,
                    "total_contributors": metrics_data.get("total_contributors", 0),
                    "recent_commits": metrics_data.get("recent_commits_30d", 0),
                    "health_score": response_data["health_score"],
                    "response_time_ms": response_time_ms
                }
            )

            return GitHubResponse(
                status="success",
                data=response_data,
                metadata=metadata
            ).dict()

    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)

        # Log error event
        await log_error_event(
            event_type="github_repository_metrics_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            repository=repo_name,
            response_time_ms=response_time_ms
        )

        logger.error(
            "Repository metrics calculation failed",
            extra={
                "repo_name": repo_name,
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )

        # Prepare error response
        error_data = {
            "repository": repo_name,
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }

        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )

        return GitHubResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {"repository": repo_name}
            }]
        ).dict()


def _calculate_repository_health_score(
    metrics: RepositoryMetrics,
    activity_data: Dict[str, Any]
) -> float:
    """
    Calculate a repository health score based on various metrics.

    Args:
        metrics: Repository metrics
        activity_data: Additional activity data

    Returns:
        float: Health score between 0 and 100
    """
    score = 0.0
    max_score = 100.0

    # Activity score (30 points)
    recent_commits = activity_data.get("recent_commits_30d", 0)
    if recent_commits > 20:
        score += 30
    elif recent_commits > 10:
        score += 20
    elif recent_commits > 5:
        score += 15
    elif recent_commits > 0:
        score += 10

    # Community engagement (25 points)
    stars = metrics.stars_count
    forks = metrics.forks_count
    if stars > 100 or forks > 50:
        score += 25
    elif stars > 50 or forks > 20:
        score += 20
    elif stars > 10 or forks > 5:
        score += 15
    elif stars > 0 or forks > 0:
        score += 10

    # Contributor diversity (20 points)
    contributors = activity_data.get("active_contributors_30d", 0)
    if contributors > 10:
        score += 20
    elif contributors > 5:
        score += 15
    elif contributors > 2:
        score += 10
    elif contributors > 0:
        score += 5

    # Pull request health (15 points)
    pr_metrics = activity_data.get("pull_requests", {})
    merge_rate = pr_metrics.get("merge_rate", 0)
    if merge_rate > 80:
        score += 15
    elif merge_rate > 60:
        score += 12
    elif merge_rate > 40:
        score += 8
    elif merge_rate > 0:
        score += 5

    # Release cadence (10 points)
    releases = activity_data.get("releases", {})
    if releases.get("total", 0) > 0:
        avg_days = releases.get("avg_days_between_releases")
        if avg_days and avg_days < 30:
            score += 10
        elif avg_days and avg_days < 90:
            score += 7
        elif avg_days and avg_days < 180:
            score += 5
        else:
            score += 3

    return round(min(score, max_score), 1)
