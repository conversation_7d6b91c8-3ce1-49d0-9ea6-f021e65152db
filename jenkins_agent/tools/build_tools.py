"""Build management tools for Jenkins Reader Agent."""

from datetime import datetime
from typing import Any, Dict, List, Optional

from google.adk.tools import ToolContext

from ..config.schemas import JenkinsResponse, ResponseMetadata, BuildInfo
from ..config.settings import get_settings
from shared_utils.auth import get_jenkins_credentials
from shared_utils.exceptions import ValidationError
from shared_utils.logging import get_logger, log_audit_event, log_error_event
from shared_utils.validation import validate_request_permissions, validate_input_parameters
from .jenkins_client import Jenkins<PERSON>lientWrapper


def _datetime_to_iso(dt: datetime) -> str:
    """Convert datetime to ISO format string for JSON serialization."""
    return dt.isoformat() + 'Z' if dt else None


def _timestamp_to_iso(timestamp: int) -> str:
    """Convert Unix timestamp (milliseconds) to ISO format string."""
    if timestamp:
        return datetime.fromtimestamp(timestamp / 1000).isoformat() + 'Z'
    return None


logger = get_logger(__name__)


async def get_build_history(
    job_name: str,
    max_builds: int = 50,
    include_details: bool = False,
    instance_name: Optional[str] = None,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Retrieve build history for a specific Jenkins job.
    
    This tool fetches the build history for a Jenkins job, including build results,
    timestamps, durations, and optionally detailed information like parameters,
    artifacts, and test results.
    
    Args:
        job_name: Name of the Jenkins job
        max_builds: Maximum number of builds to retrieve (default: 50, max: 200)
        include_details: Whether to include detailed build information
        instance_name: Optional Jenkins instance name (for future multi-instance support)
        tool_context: ADK tool context with authentication and session info
        
    Returns:
        Dict containing build history, metadata, and any warnings/errors
        
    Raises:
        ValidationError: If input parameters are invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting build history retrieval",
        extra={
            "job_name": job_name,
            "max_builds": max_builds,
            "include_details": include_details,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )
    
    start_time = datetime.utcnow()
    settings = get_settings()
    
    # Validate input parameters
    if not job_name or not job_name.strip():
        raise ValidationError(
            "Job name is required",
            field="job_name",
            value=job_name
        )
    
    job_name = job_name.strip()
    
    if max_builds <= 0 or max_builds > 200:
        raise ValidationError(
            "max_builds must be between 1 and 200",
            field="max_builds",
            value=max_builds
        )
    
    # Validate permissions
    await validate_request_permissions(tool_context, "builds.read")
    
    try:
        # Get Jenkins credentials and create client
        auth_context = getattr(tool_context, 'auth_context', None)
        user_identity = getattr(auth_context, 'user_identity', None) if auth_context else None

        if tool_context and auth_context and user_identity:
            # Check if this is a mock context (for testing)
            if hasattr(user_identity, 'user_id') and user_identity.user_id == 'test_user':
                # Use test credentials for mock context
                from shared_utils.auth import create_test_credentials
                credentials = create_test_credentials("jenkins")
            else:
                # Use real Secret Manager credentials
                credentials = await get_jenkins_credentials(tool_context)
        else:
            # For testing without context, use test credentials
            from shared_utils.auth import create_test_credentials
            credentials = create_test_credentials("jenkins")
        
        async with JenkinsClientWrapper(credentials) as jenkins_client:
            # Get job information first
            logger.debug(f"Fetching job info for: {job_name}")
            job_info = await jenkins_client.get_job_info(job_name, depth=1)
            
            # Extract builds list
            builds_list = job_info.get('builds', [])
            if not builds_list:
                logger.info(f"No builds found for job: {job_name}")
                
                # Return empty result
                response_data = {
                    "job_name": job_name,
                    "builds": [],
                    "summary": {
                        "total_builds": 0,
                        "builds_returned": 0,
                        "latest_build": None,
                        "oldest_build": None
                    }
                }
                
                end_time = datetime.utcnow()
                response_time_ms = int((end_time - start_time).total_seconds() * 1000)
                
                metadata = ResponseMetadata(
                    timestamp=_datetime_to_iso(end_time),
                    jenkins_url=str(credentials.url),
                    total_records=0,
                    query_duration_ms=response_time_ms
                )
                
                return JenkinsResponse(
                    status="success",
                    data=response_data,
                    metadata=metadata,
                    warnings=["No builds found for this job"]
                ).dict()
            
            # Limit builds to requested amount
            limited_builds = builds_list[:max_builds]
            
            # Process builds
            processed_builds = []
            detail_errors = []
            
            for build_ref in limited_builds:
                try:
                    build_number = build_ref.get('number')
                    if not build_number:
                        continue
                    
                    # Get basic build info
                    build_info = await jenkins_client.get_build_info(job_name, build_number, depth=1)
                    
                    # Extract basic build data
                    build_data = {
                        "number": build_number,
                        "result": build_info.get('result'),
                        "timestamp": build_info.get('timestamp'),
                        "duration": build_info.get('duration'),
                        "url": build_info.get('url'),
                        "building": build_info.get('building', False),
                        "queue_id": build_info.get('queueId')
                    }
                    
                    # Convert timestamp to ISO format if available
                    if build_data["timestamp"]:
                        build_data["timestamp"] = _timestamp_to_iso(build_data["timestamp"])
                    
                    # Include detailed information if requested
                    if include_details:
                        try:
                            # Extract build parameters
                            actions = build_info.get('actions', [])
                            parameters = {}
                            for action in actions:
                                if action and 'parameters' in action:
                                    for param in action['parameters']:
                                        if 'name' in param and 'value' in param:
                                            parameters[param['name']] = param['value']
                            
                            build_data["parameters"] = parameters
                            
                            # Extract artifacts
                            artifacts = []
                            for artifact in build_info.get('artifacts', []):
                                artifacts.append({
                                    "filename": artifact.get('fileName'),
                                    "relative_path": artifact.get('relativePath'),
                                    "size": artifact.get('size')
                                })
                            build_data["artifacts"] = artifacts
                            
                            # Extract test results if available
                            test_results = None
                            for action in actions:
                                if action and 'totalCount' in action:
                                    test_results = {
                                        "total": action.get('totalCount', 0),
                                        "failed": action.get('failCount', 0),
                                        "skipped": action.get('skipCount', 0),
                                        "passed": action.get('totalCount', 0) - action.get('failCount', 0) - action.get('skipCount', 0)
                                    }
                                    break
                            build_data["test_results"] = test_results
                            
                            # Extract changeset information
                            changeset = build_info.get('changeSet', {})
                            if changeset and changeset.get('items'):
                                changes = []
                                for item in changeset['items'][:5]:  # Limit to 5 changes
                                    changes.append({
                                        "author": item.get('author', {}).get('fullName', 'Unknown'),
                                        "message": item.get('msg', ''),
                                        "timestamp": item.get('timestamp')
                                    })
                                build_data["changes"] = changes
                            else:
                                build_data["changes"] = []
                                
                        except Exception as detail_error:
                            logger.warning(
                                "Failed to get detailed build information",
                                extra={
                                    "job_name": job_name,
                                    "build_number": build_number,
                                    "error": str(detail_error)
                                }
                            )
                            detail_errors.append({
                                "build_number": build_number,
                                "error": str(detail_error)
                            })
                    
                    processed_builds.append(build_data)
                    
                except Exception as build_error:
                    logger.warning(
                        "Failed to process build",
                        extra={
                            "job_name": job_name,
                            "build_number": build_ref.get('number', 'unknown'),
                            "error": str(build_error)
                        }
                    )
                    detail_errors.append({
                        "build_number": build_ref.get('number', 'unknown'),
                        "error": str(build_error)
                    })
            
            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Prepare summary statistics
            summary = {
                "total_builds": len(builds_list),
                "builds_returned": len(processed_builds),
                "latest_build": processed_builds[0]["number"] if processed_builds else None,
                "oldest_build": processed_builds[-1]["number"] if processed_builds else None,
                "detail_errors_count": len(detail_errors)
            }
            
            # Prepare response data
            response_data = {
                "job_name": job_name,
                "builds": processed_builds,
                "summary": summary
            }
            
            # Add detail errors if any
            if detail_errors:
                response_data["detail_errors"] = detail_errors
            
            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                jenkins_url=str(credentials.url),
                total_records=len(processed_builds),
                query_duration_ms=response_time_ms
            )
            
            # Prepare warnings
            warnings = []
            if len(builds_list) > max_builds:
                warnings.append(f"Results truncated: showing {len(processed_builds)} of {len(builds_list)} builds")
            if detail_errors:
                warnings.append(f"Failed to get details for {len(detail_errors)} builds")
            
            # Log successful operation
            await log_audit_event(
                event_type="jenkins_build_history_retrieved",
                user=user_identity,
                jenkins_url=str(credentials.url),
                job_name=job_name,
                builds_count=len(processed_builds),
                include_details=include_details,
                response_time_ms=response_time_ms
            )
            
            logger.info(
                "Build history retrieval successful",
                extra={
                    "job_name": job_name,
                    "builds_returned": len(processed_builds),
                    "total_builds": len(builds_list),
                    "response_time_ms": response_time_ms,
                    "include_details": include_details
                }
            )
            
            # Determine response status
            status = "success"
            if detail_errors:
                status = "warning" if processed_builds else "error"
            
            return JenkinsResponse(
                status=status,
                data=response_data,
                metadata=metadata,
                warnings=warnings
            ).dict()
            
    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)
        
        # Log error event
        await log_error_event(
            event_type="jenkins_build_history_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            job_name=job_name,
            max_builds=max_builds,
            response_time_ms=response_time_ms
        )
        
        logger.error(
            "Build history retrieval failed",
            extra={
                "job_name": job_name,
                "error": str(e),
                "max_builds": max_builds,
                "response_time_ms": response_time_ms
            }
        )
        
        # Prepare error response
        error_data = {
            "job_name": job_name,
            "builds": [],
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }
        
        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )
        
        return JenkinsResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {
                    "job_name": job_name,
                    "max_builds": max_builds
                }
            }]
        ).dict()


async def get_artifacts(
    job_name: str,
    build_number: Optional[int] = None,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Retrieve artifacts from a Jenkins build.

    This tool fetches artifacts from a specific build or the latest build
    of a Jenkins job, including artifact metadata and download information.

    Args:
        job_name: Name of the Jenkins job
        build_number: Specific build number (optional, uses latest if not provided)
        tool_context: ADK tool context with authentication and session info

    Returns:
        Dict containing artifacts information and metadata

    Raises:
        ValidationError: If input parameters are invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting artifacts retrieval",
        extra={
            "job_name": job_name,
            "build_number": build_number,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )

    start_time = datetime.utcnow()

    # Validate input parameters
    if not job_name or not job_name.strip():
        raise ValidationError(
            "Job name is required",
            field="job_name",
            value=job_name
        )

    job_name = job_name.strip()

    if build_number is not None and build_number <= 0:
        raise ValidationError(
            "Build number must be positive",
            field="build_number",
            value=build_number
        )

    # Validate permissions
    await validate_request_permissions(tool_context, "builds.read")

    try:
        # Get Jenkins credentials and create client
        auth_context = getattr(tool_context, 'auth_context', None)
        user_identity = getattr(auth_context, 'user_identity', None) if auth_context else None

        if tool_context and auth_context and user_identity:
            # Check if this is a mock context (for testing)
            if hasattr(user_identity, 'user_id') and user_identity.user_id == 'test_user':
                # Use test credentials for mock context
                from shared_utils.auth import create_test_credentials
                credentials = create_test_credentials("jenkins")
            else:
                # Use real Secret Manager credentials
                credentials = await get_jenkins_credentials(tool_context)
        else:
            # For testing without context, use test credentials
            from shared_utils.auth import create_test_credentials
            credentials = create_test_credentials("jenkins")

        async with JenkinsClientWrapper(credentials) as jenkins_client:
            # Get build number if not provided
            if build_number is None:
                logger.debug(f"Getting latest build for job: {job_name}")
                job_info = await jenkins_client.get_job_info(job_name)
                last_build = job_info.get('lastBuild')
                if not last_build:
                    # No builds available
                    response_data = {
                        "job_name": job_name,
                        "build_number": None,
                        "artifacts": [],
                        "summary": {
                            "total_artifacts": 0,
                            "total_size_bytes": 0
                        }
                    }

                    end_time = datetime.utcnow()
                    response_time_ms = int((end_time - start_time).total_seconds() * 1000)

                    metadata = ResponseMetadata(
                        timestamp=_datetime_to_iso(end_time),
                        jenkins_url=str(credentials.url),
                        total_records=0,
                        query_duration_ms=response_time_ms
                    )

                    return JenkinsResponse(
                        status="success",
                        data=response_data,
                        metadata=metadata,
                        warnings=["No builds found for this job"]
                    ).dict()

                build_number = last_build.get('number')

            # Get build information
            logger.debug(f"Fetching build info for job: {job_name}, build: {build_number}")
            build_info = await jenkins_client.get_build_info(job_name, build_number)

            # Extract artifacts
            artifacts_list = build_info.get('artifacts', [])

            # Process artifacts
            processed_artifacts = []
            total_size = 0

            for artifact in artifacts_list:
                artifact_data = {
                    "filename": artifact.get('fileName', ''),
                    "relative_path": artifact.get('relativePath', ''),
                    "size_bytes": artifact.get('size', 0),
                    "display_path": artifact.get('displayPath', ''),
                    "download_url": f"{build_info.get('url', '')}artifact/{artifact.get('relativePath', '')}"
                }

                # Add human-readable size
                size_bytes = artifact_data["size_bytes"]
                if size_bytes:
                    if size_bytes < 1024:
                        artifact_data["size_human"] = f"{size_bytes} B"
                    elif size_bytes < 1024 * 1024:
                        artifact_data["size_human"] = f"{size_bytes / 1024:.1f} KB"
                    elif size_bytes < 1024 * 1024 * 1024:
                        artifact_data["size_human"] = f"{size_bytes / (1024 * 1024):.1f} MB"
                    else:
                        artifact_data["size_human"] = f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
                else:
                    artifact_data["size_human"] = "Unknown"

                processed_artifacts.append(artifact_data)
                total_size += size_bytes

            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)

            # Prepare summary
            summary = {
                "total_artifacts": len(processed_artifacts),
                "total_size_bytes": total_size,
                "build_result": build_info.get('result'),
                "build_timestamp": _timestamp_to_iso(build_info.get('timestamp')),
                "build_duration": build_info.get('duration')
            }

            # Add human-readable total size
            if total_size:
                if total_size < 1024:
                    summary["total_size_human"] = f"{total_size} B"
                elif total_size < 1024 * 1024:
                    summary["total_size_human"] = f"{total_size / 1024:.1f} KB"
                elif total_size < 1024 * 1024 * 1024:
                    summary["total_size_human"] = f"{total_size / (1024 * 1024):.1f} MB"
                else:
                    summary["total_size_human"] = f"{total_size / (1024 * 1024 * 1024):.1f} GB"
            else:
                summary["total_size_human"] = "0 B"

            # Prepare response data
            response_data = {
                "job_name": job_name,
                "build_number": build_number,
                "artifacts": processed_artifacts,
                "summary": summary
            }

            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                jenkins_url=str(credentials.url),
                total_records=len(processed_artifacts),
                query_duration_ms=response_time_ms
            )

            # Log successful operation
            await log_audit_event(
                event_type="jenkins_artifacts_retrieved",
                user=user_identity,
                jenkins_url=str(credentials.url),
                job_name=job_name,
                build_number=build_number,
                artifacts_count=len(processed_artifacts),
                total_size_bytes=total_size,
                response_time_ms=response_time_ms
            )

            logger.info(
                "Artifacts retrieval successful",
                extra={
                    "job_name": job_name,
                    "build_number": build_number,
                    "artifacts_count": len(processed_artifacts),
                    "total_size_bytes": total_size,
                    "response_time_ms": response_time_ms
                }
            )

            return JenkinsResponse(
                status="success",
                data=response_data,
                metadata=metadata
            ).dict()

    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)

        # Log error event
        await log_error_event(
            event_type="jenkins_artifacts_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            job_name=job_name,
            build_number=build_number,
            response_time_ms=response_time_ms
        )

        logger.error(
            "Artifacts retrieval failed",
            extra={
                "job_name": job_name,
                "build_number": build_number,
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )

        # Prepare error response
        error_data = {
            "job_name": job_name,
            "build_number": build_number,
            "artifacts": [],
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }

        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )

        return JenkinsResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {
                    "job_name": job_name,
                    "build_number": build_number
                }
            }]
        ).dict()


async def get_build_by_commit_hash(
    commit_hash: str,
    job_name: Optional[str] = None,
    max_builds: int = 50,
    instance_name: Optional[str] = None,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Find Jenkins builds that contain a specific commit hash.

    This tool searches through Jenkins builds to find ones that contain
    a specific git commit hash. Essential for DORA metrics to trace
    deployments back to code changes.

    Args:
        commit_hash: The git commit hash to search for (minimum 7 characters)
        job_name: Specific job name to search in (optional, searches all jobs if not provided)
        max_builds: Maximum number of recent builds to search per job (default: 50)
        instance_name: Optional Jenkins instance name (for future multi-instance support)
        tool_context: ADK tool context with authentication and session info

    Returns:
        Dict containing matching builds with commit information

    Raises:
        ValidationError: If commit_hash is invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting commit hash search",
        extra={
            "commit_hash": commit_hash,
            "job_name": job_name,
            "max_builds": max_builds,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )

    start_time = datetime.utcnow()

    # Validate input parameters
    if not commit_hash or len(commit_hash) < 7:
        raise ValidationError(
            "Commit hash must be at least 7 characters long",
            field="commit_hash",
            value=commit_hash
        )

    if max_builds <= 0 or max_builds > 200:
        raise ValidationError(
            "max_builds must be between 1 and 200",
            field="max_builds",
            value=max_builds
        )

    # Validate permissions
    await validate_request_permissions(tool_context, "builds.read")

    try:
        # Get Jenkins credentials and create client
        auth_context = getattr(tool_context, 'auth_context', None)
        user_identity = getattr(auth_context, 'user_identity', None) if auth_context else None

        if tool_context and auth_context and user_identity:
            if hasattr(user_identity, 'user_id') and user_identity.user_id == 'test_user':
                from shared_utils.auth import create_test_credentials
                credentials = create_test_credentials("jenkins")
            else:
                credentials = await get_jenkins_credentials(tool_context)
        else:
            from shared_utils.auth import create_test_credentials
            credentials = create_test_credentials("jenkins")

        async with JenkinsClientWrapper(credentials) as jenkins_client:
            matching_builds = []
            search_info = {
                "commit_hash": commit_hash,
                "searched_jobs": [],
                "total_builds_searched": 0
            }

            # Get jobs to search
            if job_name:
                jobs_to_search = [{"name": job_name}]
            else:
                all_jobs = await jenkins_client.get_all_jobs(depth=0)
                jobs_to_search = all_jobs[:20]  # Limit to prevent timeout

            for job in jobs_to_search:
                job_name_current = job.get('name', '')
                search_info["searched_jobs"].append(job_name_current)

                try:
                    # Get job info to find recent builds
                    job_info = await jenkins_client.get_job_info(job_name_current)
                    builds = job_info.get('builds', [])
                    recent_builds = builds[:max_builds]

                    for build in recent_builds:
                        search_info["total_builds_searched"] += 1
                        build_number = build.get('number')

                        try:
                            # Get detailed build info
                            build_info = await jenkins_client.get_build_info(job_name_current, build_number)

                            # Check if build contains the commit hash
                            if _build_contains_commit(build_info, commit_hash):
                                matching_builds.append({
                                    "job_name": job_name_current,
                                    "build_number": build_number,
                                    "build_url": build_info.get('url', ''),
                                    "timestamp": build_info.get('timestamp', 0),
                                    "result": build_info.get('result', 'UNKNOWN'),
                                    "duration": build_info.get('duration', 0),
                                    "commit_hash": commit_hash,
                                    "scm_info": _extract_scm_info(build_info)
                                })

                        except Exception as build_error:
                            logger.debug(
                                "Failed to get build info",
                                extra={
                                    "job_name": job_name_current,
                                    "build_number": build_number,
                                    "error": str(build_error)
                                }
                            )
                            continue

                except Exception as job_error:
                    logger.warning(
                        "Failed to search job",
                        extra={
                            "job_name": job_name_current,
                            "error": str(job_error)
                        }
                    )
                    continue

            # Sort builds by timestamp (newest first)
            matching_builds.sort(key=lambda x: x.get('timestamp', 0), reverse=True)

            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)

            # Prepare response data
            response_data = {
                "builds": matching_builds,
                "search_info": search_info,
                "summary": {
                    "total_matches": len(matching_builds),
                    "jobs_searched": len(search_info["searched_jobs"]),
                    "builds_searched": search_info["total_builds_searched"],
                    "commit_hash": commit_hash
                }
            }

            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                jenkins_url=str(credentials.url),
                total_records=len(matching_builds),
                query_duration_ms=response_time_ms
            )

            # Log successful operation
            await log_audit_event(
                event_type="jenkins_commit_search",
                user=user_identity,
                jenkins_url=str(credentials.url),
                commit_hash=commit_hash,
                matches_found=len(matching_builds),
                response_time_ms=response_time_ms
            )

            logger.info(
                "Commit hash search completed",
                extra={
                    "commit_hash": commit_hash,
                    "matches_found": len(matching_builds),
                    "jobs_searched": len(search_info["searched_jobs"]),
                    "response_time_ms": response_time_ms
                }
            )

            return JenkinsResponse(
                status="success",
                data=response_data,
                metadata=metadata
            ).dict()

    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)

        # Log error event
        await log_error_event(
            event_type="jenkins_commit_search_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            commit_hash=commit_hash,
            response_time_ms=response_time_ms
        )

        logger.error(
            "Commit hash search failed",
            extra={
                "commit_hash": commit_hash,
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )

        # Prepare error response
        error_data = {
            "builds": [],
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }

        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )

        return JenkinsResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {"commit_hash": commit_hash}
            }]
        ).dict()


def _build_contains_commit(build_info: Dict[str, Any], commit_hash: str) -> bool:
    """
    Check if a build contains the specified commit hash.

    Args:
        build_info: Jenkins build information dictionary
        commit_hash: Git commit hash to search for

    Returns:
        bool: True if build contains the commit hash
    """
    # Check in build actions for SCM information
    actions = build_info.get('actions', [])
    for action in actions:
        if action is None:
            continue

        # Check build parameters
        parameters = action.get('parameters', [])
        for param in parameters:
            if param.get('name') == 'GIT_COMMIT' and param.get('value', '').startswith(commit_hash):
                return True

        # Check SCM revision information
        if 'lastBuiltRevision' in action:
            revision = action['lastBuiltRevision']
            if 'SHA1' in revision and revision['SHA1'].startswith(commit_hash):
                return True

        # Check remote URLs and branches
        if 'remoteUrls' in action:
            # This indicates SCM action, check for commit in build data
            pass

    # Check changeSet
    change_set = build_info.get('changeSet', {})
    items = change_set.get('items', [])
    for item in items:
        if item.get('commitId', '').startswith(commit_hash):
            return True

    return False


def _extract_scm_info(build_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract SCM (Source Control Management) information from build data.

    Args:
        build_info: Jenkins build information dictionary

    Returns:
        Dict containing SCM information
    """
    scm_info = {
        "commit_hash": None,
        "branch": None,
        "repository_url": None,
        "author": None,
        "commit_message": None,
        "commit_timestamp": None
    }

    # Check build actions for SCM information
    actions = build_info.get('actions', [])
    for action in actions:
        if action is None:
            continue

        # Check build parameters for Git info
        parameters = action.get('parameters', [])
        for param in parameters:
            param_name = param.get('name', '')
            param_value = param.get('value', '')

            if param_name == 'GIT_COMMIT':
                scm_info["commit_hash"] = param_value
            elif param_name == 'GIT_BRANCH':
                scm_info["branch"] = param_value
            elif param_name == 'GIT_URL':
                scm_info["repository_url"] = param_value

        # Check SCM revision information
        if 'lastBuiltRevision' in action:
            revision = action['lastBuiltRevision']
            if 'SHA1' in revision:
                scm_info["commit_hash"] = revision['SHA1']
            if 'branch' in revision:
                branches = revision['branch']
                if branches and len(branches) > 0:
                    scm_info["branch"] = branches[0].get('name', '')

        # Check remote URLs
        if 'remoteUrls' in action:
            urls = action['remoteUrls']
            if urls and len(urls) > 0:
                scm_info["repository_url"] = urls[0]

    # Check changeSet for commit details
    change_set = build_info.get('changeSet', {})
    items = change_set.get('items', [])
    if items and len(items) > 0:
        # Get the most recent change
        latest_change = items[0]
        scm_info["commit_hash"] = latest_change.get('commitId', scm_info["commit_hash"])
        scm_info["author"] = latest_change.get('author', {}).get('fullName', '')
        scm_info["commit_message"] = latest_change.get('msg', '')
        scm_info["commit_timestamp"] = latest_change.get('timestamp', None)

    return scm_info


async def get_builds_with_scm_data(
    job_name: Optional[str] = None,
    days_back: int = 30,
    max_builds: int = 100,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Get Jenkins builds with detailed SCM (Source Control Management) data.

    This tool extracts builds with comprehensive SCM information including
    commit hashes, branches, authors, and timestamps. Essential for DORA
    metrics Lead Time for Changes calculation.

    Args:
        job_name: Specific job name to analyze (optional, analyzes all jobs if not provided)
        days_back: Number of days to look back for build data (default: 30)
        max_builds: Maximum number of builds to retrieve per job (default: 100)
        tool_context: ADK tool context with authentication and session info

    Returns:
        Dict containing builds with detailed SCM information

    Raises:
        ValidationError: If input parameters are invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting SCM data extraction",
        extra={
            "job_name": job_name,
            "days_back": days_back,
            "max_builds": max_builds,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )

    start_time = datetime.utcnow()

    # Validate input parameters
    if days_back <= 0 or days_back > 365:
        raise ValidationError(
            "days_back must be between 1 and 365",
            field="days_back",
            value=days_back
        )

    if max_builds <= 0 or max_builds > 500:
        raise ValidationError(
            "max_builds must be between 1 and 500",
            field="max_builds",
            value=max_builds
        )

    # Validate permissions
    await validate_request_permissions(tool_context, "builds.read")

    try:
        # Get Jenkins credentials and create client
        auth_context = getattr(tool_context, 'auth_context', None)
        user_identity = getattr(auth_context, 'user_identity', None) if auth_context else None

        if tool_context and auth_context and user_identity:
            if hasattr(user_identity, 'user_id') and user_identity.user_id == 'test_user':
                from shared_utils.auth import create_test_credentials
                credentials = create_test_credentials("jenkins")
            else:
                credentials = await get_jenkins_credentials(tool_context)
        else:
            from shared_utils.auth import create_test_credentials
            credentials = create_test_credentials("jenkins")

        async with JenkinsClientWrapper(credentials) as jenkins_client:
            # Calculate time range
            end_time_ms = int(start_time.timestamp() * 1000)
            start_time_ms = int((start_time - datetime.timedelta(days=days_back)).timestamp() * 1000)

            builds_with_scm = []
            jobs_analyzed = []

            # Get jobs to analyze
            if job_name:
                jobs_to_analyze = [{"name": job_name}]
            else:
                all_jobs = await jenkins_client.get_all_jobs(depth=0)
                jobs_to_analyze = all_jobs[:15]  # Limit to prevent timeout

            for job in jobs_to_analyze:
                job_name_current = job.get('name', '')
                jobs_analyzed.append(job_name_current)

                try:
                    # Get job builds within time range
                    job_info = await jenkins_client.get_job_info(job_name_current)
                    builds = job_info.get('builds', [])
                    recent_builds = builds[:max_builds]

                    for build in recent_builds:
                        build_number = build.get('number')

                        try:
                            build_info = await jenkins_client.get_build_info(job_name_current, build_number)
                            build_timestamp = build_info.get('timestamp', 0)

                            # Check if build is within time range
                            if start_time_ms <= build_timestamp <= end_time_ms:
                                # Extract SCM information
                                scm_info = _extract_scm_info(build_info)

                                # Only include builds with SCM data
                                if scm_info.get('commit_hash'):
                                    build_data = {
                                        "job_name": job_name_current,
                                        "build_number": build_number,
                                        "timestamp": build_timestamp,
                                        "date": datetime.fromtimestamp(build_timestamp / 1000).isoformat(),
                                        "result": build_info.get('result', 'UNKNOWN'),
                                        "duration": build_info.get('duration', 0),
                                        "url": build_info.get('url', ''),
                                        "scm_info": scm_info,
                                        "build_cause": _extract_build_cause(build_info)
                                    }
                                    builds_with_scm.append(build_data)
                            elif build_timestamp < start_time_ms:
                                # Builds are ordered by number (newest first), so we can break
                                break

                        except Exception as build_error:
                            logger.debug(
                                "Failed to get build info for SCM analysis",
                                extra={
                                    "job_name": job_name_current,
                                    "build_number": build_number,
                                    "error": str(build_error)
                                }
                            )
                            continue

                except Exception as job_error:
                    logger.warning(
                        "Failed to analyze job for SCM data",
                        extra={
                            "job_name": job_name_current,
                            "error": str(job_error)
                        }
                    )
                    continue

            # Sort builds by timestamp
            builds_with_scm.sort(key=lambda x: x['timestamp'])

            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)

            # Prepare response data
            response_data = {
                "builds": builds_with_scm,
                "summary": {
                    "total_builds_with_scm": len(builds_with_scm),
                    "jobs_analyzed": len(jobs_analyzed),
                    "days_analyzed": days_back,
                    "time_range": {
                        "start": datetime.fromtimestamp(start_time_ms / 1000).isoformat(),
                        "end": datetime.fromtimestamp(end_time_ms / 1000).isoformat()
                    }
                },
                "analysis_info": {
                    "jobs_analyzed": jobs_analyzed,
                    "max_builds_per_job": max_builds
                }
            }

            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                jenkins_url=str(credentials.url),
                total_records=len(builds_with_scm),
                query_duration_ms=response_time_ms
            )

            # Log successful operation
            await log_audit_event(
                event_type="jenkins_scm_data_extraction",
                user=user_identity,
                jenkins_url=str(credentials.url),
                builds_found=len(builds_with_scm),
                days_analyzed=days_back,
                response_time_ms=response_time_ms
            )

            logger.info(
                "SCM data extraction completed",
                extra={
                    "builds_found": len(builds_with_scm),
                    "jobs_analyzed": len(jobs_analyzed),
                    "response_time_ms": response_time_ms
                }
            )

            return JenkinsResponse(
                status="success",
                data=response_data,
                metadata=metadata
            ).dict()

    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)

        # Log error event
        await log_error_event(
            event_type="jenkins_scm_data_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            days_back=days_back,
            response_time_ms=response_time_ms
        )

        logger.error(
            "SCM data extraction failed",
            extra={
                "error": str(e),
                "days_back": days_back,
                "response_time_ms": response_time_ms
            }
        )

        # Prepare error response
        error_data = {
            "builds": [],
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }

        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )

        return JenkinsResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {"days_back": days_back}
            }]
        ).dict()


def _extract_build_cause(build_info: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract build cause information from build data.

    Args:
        build_info: Jenkins build information dictionary

    Returns:
        Dict containing build cause information
    """
    build_cause = {
        "triggered_by": "unknown",
        "trigger_type": "unknown",
        "upstream_project": None,
        "upstream_build": None,
        "user": None
    }

    actions = build_info.get('actions', [])
    for action in actions:
        if action is None:
            continue

        # Check for cause information
        causes = action.get('causes', [])
        for cause in causes:
            cause_class = cause.get('_class', '')

            if 'UserIdCause' in cause_class:
                build_cause["triggered_by"] = "user"
                build_cause["trigger_type"] = "manual"
                build_cause["user"] = cause.get('userId', '')
            elif 'SCMTrigger' in cause_class:
                build_cause["triggered_by"] = "scm"
                build_cause["trigger_type"] = "scm_change"
            elif 'TimerTrigger' in cause_class:
                build_cause["triggered_by"] = "timer"
                build_cause["trigger_type"] = "scheduled"
            elif 'UpstreamCause' in cause_class:
                build_cause["triggered_by"] = "upstream"
                build_cause["trigger_type"] = "upstream_project"
                build_cause["upstream_project"] = cause.get('upstreamProject', '')
                build_cause["upstream_build"] = cause.get('upstreamBuild', '')

    return build_cause


async def get_deployment_builds(
    days_back: int = 30,
    deployment_pattern: Optional[str] = None,
    environment: Optional[str] = None,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Get Jenkins deployment builds for DORA metrics calculation.

    This tool identifies and extracts deployment builds with their success/failure
    status, timestamps, and environment information. Essential for calculating
    Deployment Frequency and Change Failure Rate DORA metrics.

    Args:
        days_back: Number of days to look back for deployment data (default: 30)
        deployment_pattern: Regex pattern to identify deployment jobs (optional)
        environment: Specific environment to filter deployments (optional)
        tool_context: ADK tool context with authentication and session info

    Returns:
        Dict containing deployment builds with success/failure information

    Raises:
        ValidationError: If input parameters are invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting deployment builds extraction",
        extra={
            "days_back": days_back,
            "deployment_pattern": deployment_pattern,
            "environment": environment,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )

    start_time = datetime.utcnow()

    # Validate input parameters
    if days_back <= 0 or days_back > 365:
        raise ValidationError(
            "days_back must be between 1 and 365",
            field="days_back",
            value=days_back
        )

    # Validate permissions
    await validate_request_permissions(tool_context, "builds.read")

    try:
        # Get Jenkins credentials and create client
        auth_context = getattr(tool_context, 'auth_context', None)
        user_identity = getattr(auth_context, 'user_identity', None) if auth_context else None

        if tool_context and auth_context and user_identity:
            if hasattr(user_identity, 'user_id') and user_identity.user_id == 'test_user':
                from shared_utils.auth import create_test_credentials
                credentials = create_test_credentials("jenkins")
            else:
                credentials = await get_jenkins_credentials(tool_context)
        else:
            from shared_utils.auth import create_test_credentials
            credentials = create_test_credentials("jenkins")

        async with JenkinsClientWrapper(credentials) as jenkins_client:
            # Calculate time range
            end_time_ms = int(start_time.timestamp() * 1000)
            start_time_ms = int((start_time - datetime.timedelta(days=days_back)).timestamp() * 1000)

            deployment_builds = []
            jobs_analyzed = []

            # Get all jobs
            all_jobs = await jenkins_client.get_all_jobs(depth=0)

            # Filter for deployment jobs
            deployment_jobs = []
            if deployment_pattern:
                import re
                pattern = re.compile(deployment_pattern, re.IGNORECASE)
                deployment_jobs = [job for job in all_jobs if pattern.search(job.get('name', ''))]
            else:
                # Look for common deployment job patterns
                deployment_keywords = ['deploy', 'release', 'prod', 'production', 'staging', 'publish']
                deployment_jobs = [
                    job for job in all_jobs
                    if any(keyword in job.get('name', '').lower() for keyword in deployment_keywords)
                ]

            # If environment filter is specified, further filter jobs
            if environment:
                env_filtered_jobs = [
                    job for job in deployment_jobs
                    if environment.lower() in job.get('name', '').lower()
                ]
                if env_filtered_jobs:
                    deployment_jobs = env_filtered_jobs

            for job in deployment_jobs:
                job_name_current = job.get('name', '')
                jobs_analyzed.append(job_name_current)

                try:
                    # Get job builds within time range
                    job_info = await jenkins_client.get_job_info(job_name_current)
                    builds = job_info.get('builds', [])

                    for build in builds:
                        build_number = build.get('number')

                        try:
                            build_info = await jenkins_client.get_build_info(job_name_current, build_number)
                            build_timestamp = build_info.get('timestamp', 0)

                            # Check if build is within time range
                            if start_time_ms <= build_timestamp <= end_time_ms:
                                result = build_info.get('result', '')

                                # Extract deployment information
                                deployment_data = {
                                    "job_name": job_name_current,
                                    "build_number": build_number,
                                    "timestamp": build_timestamp,
                                    "date": datetime.fromtimestamp(build_timestamp / 1000).isoformat(),
                                    "result": result,
                                    "duration": build_info.get('duration', 0),
                                    "url": build_info.get('url', ''),
                                    "is_successful": result == 'SUCCESS',
                                    "is_failure": result in ['FAILURE', 'ABORTED', 'UNSTABLE'],
                                    "environment": _extract_environment(job_name_current, build_info),
                                    "scm_info": _extract_scm_info(build_info),
                                    "build_cause": _extract_build_cause(build_info)
                                }

                                deployment_builds.append(deployment_data)
                            elif build_timestamp < start_time_ms:
                                # Builds are ordered by number (newest first), so we can break
                                break

                        except Exception as build_error:
                            logger.debug(
                                "Failed to get build info for deployment analysis",
                                extra={
                                    "job_name": job_name_current,
                                    "build_number": build_number,
                                    "error": str(build_error)
                                }
                            )
                            continue

                except Exception as job_error:
                    logger.warning(
                        "Failed to analyze deployment job",
                        extra={
                            "job_name": job_name_current,
                            "error": str(job_error)
                        }
                    )
                    continue

            # Sort deployments by timestamp
            deployment_builds.sort(key=lambda x: x['timestamp'])

            # Calculate metrics
            total_deployments = len(deployment_builds)
            successful_deployments = len([d for d in deployment_builds if d['is_successful']])
            failed_deployments = len([d for d in deployment_builds if d['is_failure']])

            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)

            # Prepare response data
            response_data = {
                "deployments": deployment_builds,
                "metrics": {
                    "total_deployments": total_deployments,
                    "successful_deployments": successful_deployments,
                    "failed_deployments": failed_deployments,
                    "success_rate_percent": round((successful_deployments / total_deployments * 100) if total_deployments > 0 else 0, 2),
                    "failure_rate_percent": round((failed_deployments / total_deployments * 100) if total_deployments > 0 else 0, 2),
                    "deployment_frequency_per_day": round(total_deployments / days_back, 2),
                    "days_analyzed": days_back
                },
                "analysis_info": {
                    "time_range": {
                        "start": datetime.fromtimestamp(start_time_ms / 1000).isoformat(),
                        "end": datetime.fromtimestamp(end_time_ms / 1000).isoformat()
                    },
                    "jobs_analyzed": jobs_analyzed,
                    "deployment_pattern": deployment_pattern,
                    "environment_filter": environment
                }
            }

            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                jenkins_url=str(credentials.url),
                total_records=total_deployments,
                query_duration_ms=response_time_ms
            )

            # Log successful operation
            await log_audit_event(
                event_type="jenkins_deployment_builds_extraction",
                user=user_identity,
                jenkins_url=str(credentials.url),
                deployments_found=total_deployments,
                days_analyzed=days_back,
                response_time_ms=response_time_ms
            )

            logger.info(
                "Deployment builds extraction completed",
                extra={
                    "deployments_found": total_deployments,
                    "successful_deployments": successful_deployments,
                    "failed_deployments": failed_deployments,
                    "jobs_analyzed": len(jobs_analyzed),
                    "response_time_ms": response_time_ms
                }
            )

            return JenkinsResponse(
                status="success",
                data=response_data,
                metadata=metadata
            ).dict()

    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)

        # Log error event
        await log_error_event(
            event_type="jenkins_deployment_builds_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            days_back=days_back,
            response_time_ms=response_time_ms
        )

        logger.error(
            "Deployment builds extraction failed",
            extra={
                "error": str(e),
                "days_back": days_back,
                "response_time_ms": response_time_ms
            }
        )

        # Prepare error response
        error_data = {
            "deployments": [],
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }

        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )

        return JenkinsResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {"days_back": days_back}
            }]
        ).dict()


def _extract_environment(job_name: str, build_info: Dict[str, Any]) -> str:
    """
    Extract environment information from job name and build data.

    Args:
        job_name: Jenkins job name
        build_info: Jenkins build information dictionary

    Returns:
        str: Environment name (production, staging, development, etc.)
    """
    # Check job name for environment indicators
    job_name_lower = job_name.lower()

    if any(keyword in job_name_lower for keyword in ['prod', 'production']):
        return 'production'
    elif any(keyword in job_name_lower for keyword in ['stag', 'staging']):
        return 'staging'
    elif any(keyword in job_name_lower for keyword in ['dev', 'development']):
        return 'development'
    elif any(keyword in job_name_lower for keyword in ['test', 'testing']):
        return 'testing'
    elif any(keyword in job_name_lower for keyword in ['qa', 'quality']):
        return 'qa'

    # Check build parameters for environment
    actions = build_info.get('actions', [])
    for action in actions:
        if action is None:
            continue

        parameters = action.get('parameters', [])
        for param in parameters:
            param_name = param.get('name', '').lower()
            param_value = param.get('value', '').lower()

            if 'env' in param_name or 'environment' in param_name:
                return param_value

    return 'unknown'


async def get_build_test_results(
    job_name: str,
    build_number: Optional[int] = None,
    max_builds: int = 10,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Get test results from Jenkins builds for quality metrics.

    This tool extracts test results including pass/fail counts, test duration,
    and test failure details. Useful for understanding build quality and
    identifying patterns in test failures that may contribute to DORA metrics.

    Args:
        job_name: Name of the Jenkins job
        build_number: Specific build number (optional, gets recent builds if not provided)
        max_builds: Maximum number of builds to analyze if build_number not specified (default: 10)
        tool_context: ADK tool context with authentication and session info

    Returns:
        Dict containing test results and quality metrics

    Raises:
        ValidationError: If input parameters are invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting test results extraction",
        extra={
            "job_name": job_name,
            "build_number": build_number,
            "max_builds": max_builds,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )

    start_time = datetime.utcnow()

    # Validate input parameters
    if not job_name or not job_name.strip():
        raise ValidationError(
            "Job name is required",
            field="job_name",
            value=job_name
        )

    if max_builds <= 0 or max_builds > 50:
        raise ValidationError(
            "max_builds must be between 1 and 50",
            field="max_builds",
            value=max_builds
        )

    job_name = job_name.strip()

    # Validate permissions
    await validate_request_permissions(tool_context, "builds.read")

    try:
        # Get Jenkins credentials and create client
        auth_context = getattr(tool_context, 'auth_context', None)
        user_identity = getattr(auth_context, 'user_identity', None) if auth_context else None

        if tool_context and auth_context and user_identity:
            if hasattr(user_identity, 'user_id') and user_identity.user_id == 'test_user':
                from shared_utils.auth import create_test_credentials
                credentials = create_test_credentials("jenkins")
            else:
                credentials = await get_jenkins_credentials(tool_context)
        else:
            from shared_utils.auth import create_test_credentials
            credentials = create_test_credentials("jenkins")

        async with JenkinsClientWrapper(credentials) as jenkins_client:
            test_results = []

            if build_number:
                # Get specific build
                builds_to_analyze = [build_number]
            else:
                # Get recent builds
                job_info = await jenkins_client.get_job_info(job_name)
                builds = job_info.get('builds', [])
                builds_to_analyze = [build.get('number') for build in builds[:max_builds]]

            for build_num in builds_to_analyze:
                try:
                    build_info = await jenkins_client.get_build_info(job_name, build_num)

                    # Extract test results
                    test_data = _extract_test_results(build_info)
                    if test_data:
                        test_results.append({
                            "job_name": job_name,
                            "build_number": build_num,
                            "timestamp": build_info.get('timestamp', 0),
                            "date": datetime.fromtimestamp(build_info.get('timestamp', 0) / 1000).isoformat(),
                            "result": build_info.get('result', 'UNKNOWN'),
                            "duration": build_info.get('duration', 0),
                            "url": build_info.get('url', ''),
                            "test_results": test_data
                        })

                except Exception as build_error:
                    logger.debug(
                        "Failed to get test results for build",
                        extra={
                            "job_name": job_name,
                            "build_number": build_num,
                            "error": str(build_error)
                        }
                    )
                    continue

            # Calculate aggregate metrics
            total_tests = sum(tr['test_results']['total_count'] for tr in test_results)
            total_passed = sum(tr['test_results']['pass_count'] for tr in test_results)
            total_failed = sum(tr['test_results']['fail_count'] for tr in test_results)
            total_skipped = sum(tr['test_results']['skip_count'] for tr in test_results)

            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)

            # Prepare response data
            response_data = {
                "test_results": test_results,
                "summary": {
                    "builds_analyzed": len(test_results),
                    "total_tests": total_tests,
                    "total_passed": total_passed,
                    "total_failed": total_failed,
                    "total_skipped": total_skipped,
                    "pass_rate_percent": round((total_passed / total_tests * 100) if total_tests > 0 else 0, 2),
                    "fail_rate_percent": round((total_failed / total_tests * 100) if total_tests > 0 else 0, 2)
                }
            }

            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                jenkins_url=str(credentials.url),
                total_records=len(test_results),
                query_duration_ms=response_time_ms
            )

            # Log successful operation
            await log_audit_event(
                event_type="jenkins_test_results_extraction",
                user=user_identity,
                jenkins_url=str(credentials.url),
                job_name=job_name,
                builds_analyzed=len(test_results),
                total_tests=total_tests,
                response_time_ms=response_time_ms
            )

            logger.info(
                "Test results extraction completed",
                extra={
                    "job_name": job_name,
                    "builds_analyzed": len(test_results),
                    "total_tests": total_tests,
                    "pass_rate": round((total_passed / total_tests * 100) if total_tests > 0 else 0, 2),
                    "response_time_ms": response_time_ms
                }
            )

            return JenkinsResponse(
                status="success",
                data=response_data,
                metadata=metadata
            ).dict()

    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)

        # Log error event
        await log_error_event(
            event_type="jenkins_test_results_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            job_name=job_name,
            response_time_ms=response_time_ms
        )

        logger.error(
            "Test results extraction failed",
            extra={
                "job_name": job_name,
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )

        # Prepare error response
        error_data = {
            "test_results": [],
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }

        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )

        return JenkinsResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {"job_name": job_name}
            }]
        ).dict()


def _extract_test_results(build_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Extract test results from build information.

    Args:
        build_info: Jenkins build information dictionary

    Returns:
        Dict containing test results or None if no test data found
    """
    actions = build_info.get('actions', [])

    for action in actions:
        if action is None:
            continue

        # Look for test result actions
        action_class = action.get('_class', '')

        if 'TestResultAction' in action_class:
            return {
                "total_count": action.get('totalCount', 0),
                "fail_count": action.get('failCount', 0),
                "pass_count": action.get('totalCount', 0) - action.get('failCount', 0) - action.get('skipCount', 0),
                "skip_count": action.get('skipCount', 0),
                "url_name": action.get('urlName', ''),
                "failed_tests": action.get('failedTests', [])
            }
        elif 'AggregatedTestResultAction' in action_class:
            return {
                "total_count": action.get('totalCount', 0),
                "fail_count": action.get('failCount', 0),
                "pass_count": action.get('totalCount', 0) - action.get('failCount', 0) - action.get('skipCount', 0),
                "skip_count": action.get('skipCount', 0),
                "url_name": action.get('urlName', ''),
                "failed_tests": []
            }

    return None
