# ============================================================================
# ADK Analyst Agents - Development Dependencies
# Additional requirements for development, testing, and code quality
# ============================================================================

# Include production requirements
-r requirements.txt

# Testing Framework
pytest==8.4.0
pytest-asyncio==1.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-xdist>=3.0.0

# Code Formatting and Linting
black==25.1.0
isort==6.0.1
flake8>=6.0.0
autopep8>=2.0.0

# Type Checking
mypy==1.16.0
mypy-extensions==1.1.0
types-requests>=2.31.0
types-PyYAML>=6.0.0

# Code Quality and Security
bandit>=1.7.0
safety>=2.3.0
pre-commit>=3.0.0

# Documentation
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0
myst-parser>=1.0.0

# Development Utilities
ipython>=8.0.0
jupyter>=1.0.0
notebook>=6.5.0

# Performance Profiling
memory-profiler>=0.60.0
line-profiler>=4.0.0

# API Testing
httpx[cli]==0.28.1
requests-mock>=1.10.0

# Environment Management
python-dotenv==1.1.0
environs>=9.5.0

# Build Tools
build>=0.10.0
twine>=4.0.0
wheel>=0.40.0
