# ============================================================================
# ADK Analyst Agents - Enterprise DevOps Analytics Platform
# Requirements for Jenkins and GitHub agents with shared utilities
# ============================================================================

# Core ADK Framework
google-adk==1.2.1

# Agent Framework Dependencies
pydantic==2.11.5
pydantic-settings==2.9.1
pydantic-core==2.33.2
typing-extensions==4.14.0

# Jenkins Integration
python-jenkins==1.8.2
multi-key-dict==2.0.3
pbr==6.1.1

# GitHub Integration
pygithub==2.6.1
pynacl==1.5.0
pyjwt==2.10.1
deprecated==1.2.18

# HTTP and API Clients
httpx==0.28.1
httpcore==1.0.9
aiohttp==3.12.9
aiosignal==1.3.2
aiohappyeyeballs==2.6.1
frozenlist==1.6.2
multidict==6.4.4
propcache==0.3.1
yarl==1.20.0
attrs==25.3.0
h11==0.16.0

# Google Cloud Services
google-cloud-secret-manager==2.23.3
google-cloud-iam==2.19.0
google-cloud-logging==3.12.1
google-cloud-core==2.4.3
google-cloud-storage==2.19.0
google-cloud-aiplatform==1.95.1
google-cloud-bigquery==3.34.0
google-cloud-resource-manager==1.14.2
google-cloud-speech==2.32.0
google-cloud-trace==1.16.1
google-cloud-appengine-logging==1.6.1
google-cloud-audit-log==0.3.2

# Google API and Auth
google-api-python-client==2.170.0
google-api-core==2.25.0
google-auth==2.40.2
google-auth-httplib2==0.2.0
google-genai==1.18.0
google-crc32c==1.7.1
google-resumable-media==2.7.2
googleapis-common-protos==1.70.0

# Authentication and Security
authlib==1.6.0
cryptography==45.0.3
cffi==1.17.1
pycparser==2.22

# Performance and Utilities
asyncio-throttle==1.0.2
tenacity==8.5.0
xmltodict==0.13.0
cachetools==5.5.2

# Logging and Monitoring
structlog==23.3.0
opentelemetry-api==1.33.1
opentelemetry-sdk==1.33.1
opentelemetry-exporter-gcp-trace==1.9.0
opentelemetry-resourcedetector-gcp==1.9.0a0
opentelemetry-semantic-conventions==0.54b1

# Web Framework (for ADK web interface)
fastapi==0.115.12
starlette==0.46.2
uvicorn==0.34.3
sse-starlette==2.3.6
python-multipart==0.0.20
httpx-sse==0.4.0

# MCP (Model Context Protocol)
mcp==1.9.2

# Database
sqlalchemy==2.0.41
greenlet==3.2.2

# Utilities
click==8.2.1
python-dotenv==1.1.0
pyyaml==6.0.2
tzlocal==5.3.1
packaging==25.0
six==1.17.0
certifi==2025.4.26
charset-normalizer==3.4.2
idna==3.10
urllib3==2.4.0
requests==2.32.3
python-dateutil==2.9.0.post0
uritemplate==4.2.0

# Async and Concurrency
anyio==4.9.0
sniffio==1.3.1
websockets==15.0.1

# Cryptography and Security
rsa==4.9.1
pyasn1==0.6.1
pyasn1-modules==0.4.2

# gRPC
grpcio==1.72.1
grpcio-status==1.72.1
grpc-google-iam-v1==0.14.2

# Protocol Buffers
protobuf==6.31.1
proto-plus==1.26.1

# Scientific Computing (for data analysis)
numpy==2.2.6
shapely==2.1.1

# Additional Utilities
wrapt==1.17.2
importlib-metadata==8.6.1
zipp==3.22.0
typing-inspection==0.4.1
annotated-types==0.7.0
cloudpickle==3.1.1
graphviz==0.20.3
docstring-parser==0.16

# Development Dependencies
pytest==8.4.0
pytest-asyncio==1.0.0
black==25.1.0
isort==6.0.1
mypy==1.16.0
mypy-extensions==1.1.0
pathspec==0.12.1
platformdirs==4.3.8
pluggy==1.6.0
iniconfig==2.1.0

# Documentation and Formatting
pygments==2.19.1
bleach==6.2.0
webencodings==0.5.1

# HTTP Libraries
httplib2==0.22.0
pyparsing==3.2.3

# DNS
dnspython==2.7.0

# Email Validation
email-validator==2.2.0
