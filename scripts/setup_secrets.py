#!/usr/bin/env python3
"""
Setup script for storing credentials in Google Secret Manager.
This script helps users migrate from environment variables to Secret Manager.
"""

import os
import json
import asyncio
import argparse
from typing import Dict, Any

from google.cloud import secretmanager
from google.api_core import exceptions as gcp_exceptions


def get_project_id() -> str:
    """Get Google Cloud project ID from environment."""
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
    if not project_id:
        raise ValueError("GOOGLE_CLOUD_PROJECT environment variable not set")
    return project_id


def create_secret_manager_client() -> secretmanager.SecretManagerServiceClient:
    """Create Secret Manager client."""
    try:
        return secretmanager.SecretManagerServiceClient()
    except Exception as e:
        raise RuntimeError(f"Failed to create Secret Manager client: {e}")


def create_secret(client: secretmanager.SecretManagerServiceClient, 
                 project_id: str, secret_id: str, secret_value: str) -> str:
    """
    Create a secret in Google Secret Manager.
    
    Args:
        client: Secret Manager client
        project_id: Google Cloud project ID
        secret_id: Secret identifier
        secret_value: Secret value to store
        
    Returns:
        Resource name of the created secret version
    """
    parent = f"projects/{project_id}"
    
    try:
        # Try to create the secret
        secret = client.create_secret(
            request={
                "parent": parent,
                "secret_id": secret_id,
                "secret": {"replication": {"automatic": {}}},
            }
        )
        print(f"✅ Created secret: {secret_id}")
    except gcp_exceptions.AlreadyExists:
        print(f"ℹ️  Secret {secret_id} already exists, updating...")
        secret = client.get_secret(request={"name": f"{parent}/secrets/{secret_id}"})
    
    # Add the secret version
    version = client.add_secret_version(
        request={
            "parent": secret.name,
            "payload": {"data": secret_value.encode("UTF-8")},
        }
    )
    
    print(f"✅ Added version to secret: {secret_id}")
    return version.name


def setup_jenkins_credentials(client: secretmanager.SecretManagerServiceClient, 
                             project_id: str) -> bool:
    """Setup Jenkins credentials in Secret Manager."""
    print("\n🔧 Setting up Jenkins credentials...")
    
    jenkins_url = os.getenv('JENKINS_URL')
    jenkins_username = os.getenv('JENKINS_USERNAME')
    jenkins_password = os.getenv('JENKINS_PASSWORD')
    jenkins_token = os.getenv('JENKINS_TOKEN')
    
    if not jenkins_url:
        print("❌ JENKINS_URL environment variable not set")
        return False
    
    if not jenkins_username:
        print("❌ JENKINS_USERNAME environment variable not set")
        return False
    
    if not (jenkins_password or jenkins_token):
        print("❌ Either JENKINS_PASSWORD or JENKINS_TOKEN must be set")
        return False
    
    credentials = {
        "url": jenkins_url,
        "username": jenkins_username,
        "password": jenkins_password,
        "token": jenkins_token,
        "service_name": "jenkins"
    }
    
    try:
        create_secret(
            client, 
            project_id, 
            "jenkins-credentials", 
            json.dumps(credentials, indent=2)
        )
        print("✅ Jenkins credentials stored successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to store Jenkins credentials: {e}")
        return False


def setup_github_credentials(client: secretmanager.SecretManagerServiceClient, 
                            project_id: str) -> bool:
    """Setup GitHub credentials in Secret Manager."""
    print("\n🐙 Setting up GitHub credentials...")
    
    github_token = os.getenv('GITHUB_TOKEN') or os.getenv('GITHUB_PERSONAL_ACCESS_TOKEN')
    
    if not github_token:
        print("❌ GITHUB_TOKEN or GITHUB_PERSONAL_ACCESS_TOKEN environment variable not set")
        return False
    
    credentials = {
        "token": github_token,
        "personal_access_token": github_token,
        "service_name": "github"
    }
    
    try:
        create_secret(
            client, 
            project_id, 
            "github-credentials", 
            json.dumps(credentials, indent=2)
        )
        print("✅ GitHub credentials stored successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to store GitHub credentials: {e}")
        return False


def verify_secrets(client: secretmanager.SecretManagerServiceClient, 
                  project_id: str) -> None:
    """Verify that secrets were created successfully."""
    print("\n🔍 Verifying secrets...")
    
    secrets_to_check = ["jenkins-credentials", "github-credentials"]
    
    for secret_id in secrets_to_check:
        try:
            secret_path = f"projects/{project_id}/secrets/{secret_id}/versions/latest"
            response = client.access_secret_version(request={"name": secret_path})
            secret_data = response.payload.data.decode("UTF-8")
            
            # Parse and validate JSON
            credentials = json.loads(secret_data)
            print(f"✅ {secret_id}: Valid JSON with {len(credentials)} fields")
            
        except gcp_exceptions.NotFound:
            print(f"❌ {secret_id}: Not found")
        except json.JSONDecodeError:
            print(f"❌ {secret_id}: Invalid JSON")
        except Exception as e:
            print(f"❌ {secret_id}: Error - {e}")


def main():
    """Main setup function."""
    parser = argparse.ArgumentParser(description="Setup ADK Analyst credentials in Google Secret Manager")
    parser.add_argument("--verify-only", action="store_true", help="Only verify existing secrets")
    parser.add_argument("--jenkins-only", action="store_true", help="Setup only Jenkins credentials")
    parser.add_argument("--github-only", action="store_true", help="Setup only GitHub credentials")
    
    args = parser.parse_args()
    
    print("🚀 ADK Analyst - Secret Manager Setup")
    print("=" * 50)
    
    try:
        # Get project ID and create client
        project_id = get_project_id()
        print(f"📊 Project ID: {project_id}")
        
        client = create_secret_manager_client()
        print("✅ Secret Manager client created")
        
        if args.verify_only:
            verify_secrets(client, project_id)
            return
        
        success_count = 0
        total_count = 0
        
        # Setup Jenkins credentials
        if not args.github_only:
            total_count += 1
            if setup_jenkins_credentials(client, project_id):
                success_count += 1
        
        # Setup GitHub credentials
        if not args.jenkins_only:
            total_count += 1
            if setup_github_credentials(client, project_id):
                success_count += 1
        
        # Verify all secrets
        verify_secrets(client, project_id)
        
        print(f"\n🎉 Setup complete: {success_count}/{total_count} credentials stored successfully")
        
        if success_count == total_count:
            print("\n✅ All credentials stored successfully!")
            print("💡 You can now set USE_SECRET_MANAGER=true in your .env file")
            print("💡 Environment variables will be used as fallback if Secret Manager fails")
        else:
            print("\n⚠️  Some credentials failed to store. Check the errors above.")
            
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
