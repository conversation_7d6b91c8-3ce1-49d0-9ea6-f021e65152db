"""Shared authentication utilities for enterprise agents."""

import json
import os
from typing import Any, Dict, Optional, Type, TypeVar

from google.cloud import secretmanager
from google.adk.tools import ToolContext

from .exceptions import AuthenticationError, ConfigurationError
from .logging import get_logger
from .models import ServiceCredentials, GitHubCredentials, JenkinsCredentials, JFrogCredentials

logger = get_logger(__name__)

T = TypeVar('T', bound=ServiceCredentials)


async def get_credentials_from_secret_manager(
    secret_name: str,
    credentials_class: Type[T],
    project_id: Optional[str] = None
) -> T:
    """
    Retrieve service credentials from Google Secret Manager.
    
    Args:
        secret_name: Name of the secret in Secret Manager
        credentials_class: Pydantic model class for credentials
        project_id: Google Cloud project ID (optional, uses environment if not provided)
        
    Returns:
        Credentials instance of the specified type
        
    Raises:
        AuthenticationError: If credentials cannot be retrieved or are invalid
        ConfigurationError: If configuration is missing or invalid
    """
    try:
        # Get project ID from environment if not provided
        if not project_id:
            project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
            if not project_id:
                raise ConfigurationError(
                    "Google Cloud project ID not found",
                    config_key="GOOGLE_CLOUD_PROJECT"
                )
        
        # Construct the secret path
        secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
        
        logger.info(
            "Retrieving credentials from Secret Manager",
            extra={
                "secret_name": secret_name,
                "project_id": project_id,
                "credentials_type": credentials_class.__name__
            }
        )
        
        # Create Secret Manager client
        client = secretmanager.SecretManagerServiceClient()
        
        # Access the secret
        response = client.access_secret_version(request={"name": secret_path})
        secret_data = response.payload.data.decode("UTF-8")
        
        # Parse and validate credentials
        try:
            credentials_dict = json.loads(secret_data)
            credentials = credentials_class(**credentials_dict)
            
            logger.info(
                "Successfully retrieved credentials",
                extra={
                    "secret_name": secret_name,
                    "credentials_type": credentials_class.__name__,
                    "service_name": credentials.service_name
                }
            )
            
            return credentials
            
        except json.JSONDecodeError as e:
            raise AuthenticationError(
                f"Invalid JSON in secret {secret_name}: {str(e)}",
                details={"secret_name": secret_name}
            )
        except Exception as e:
            raise AuthenticationError(
                f"Invalid credentials format in secret {secret_name}: {str(e)}",
                details={"secret_name": secret_name}
            )
            
    except Exception as e:
        if isinstance(e, (AuthenticationError, ConfigurationError)):
            raise
        
        logger.error(
            "Failed to retrieve credentials from Secret Manager",
            extra={
                "secret_name": secret_name,
                "error": str(e)
            }
        )
        
        raise AuthenticationError(
            f"Failed to retrieve credentials: {str(e)}",
            details={"secret_name": secret_name}
        )


async def validate_token_credentials(
    credentials: ServiceCredentials,
    validation_url: Optional[str] = None
) -> bool:
    """
    Validate service credentials by making a test API call.
    
    Args:
        credentials: Service credentials to validate
        validation_url: Optional URL for validation (service-specific)
        
    Returns:
        bool: True if credentials are valid, False otherwise
    """
    try:
        if isinstance(credentials, GitHubCredentials):
            return await _validate_github_credentials(credentials)
        elif isinstance(credentials, JenkinsCredentials):
            return await _validate_jenkins_credentials(credentials)
        elif isinstance(credentials, JFrogCredentials):
            return await _validate_jfrog_credentials(credentials)
        else:
            # Generic token validation
            return await _validate_generic_credentials(credentials, validation_url)
            
    except Exception as e:
        logger.error(
            "Credential validation failed",
            extra={
                "service_name": credentials.service_name,
                "error": str(e)
            }
        )
        return False


async def _validate_github_credentials(credentials: GitHubCredentials) -> bool:
    """Validate GitHub credentials."""
    import aiohttp
    
    headers = {
        "Authorization": f"token {credentials.token}",
        "Accept": "application/vnd.github.v3+json",
        "X-GitHub-Api-Version": credentials.api_version
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.get("https://api.github.com/user", headers=headers) as response:
            if response.status == 200:
                user_data = await response.json()
                logger.info(
                    "GitHub credentials validated successfully",
                    extra={
                        "username": user_data.get("login"),
                        "user_id": user_data.get("id")
                    }
                )
                return True
            else:
                logger.warning(
                    "GitHub credential validation failed",
                    extra={"status_code": response.status}
                )
                return False


async def _validate_jenkins_credentials(credentials: JenkinsCredentials) -> bool:
    """Validate Jenkins credentials."""
    import aiohttp
    import base64
    
    # Create basic auth header
    auth_string = f"{credentials.username}:{credentials.token}"
    auth_bytes = auth_string.encode('ascii')
    auth_header = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        "Authorization": f"Basic {auth_header}",
        "Content-Type": "application/json"
    }
    
    validation_url = f"{credentials.url}/api/json"
    
    async with aiohttp.ClientSession() as session:
        async with session.get(validation_url, headers=headers) as response:
            if response.status == 200:
                logger.info("Jenkins credentials validated successfully")
                return True
            else:
                logger.warning(
                    "Jenkins credential validation failed",
                    extra={"status_code": response.status}
                )
                return False


async def _validate_jfrog_credentials(credentials: JFrogCredentials) -> bool:
    """Validate JFrog credentials."""
    import aiohttp
    import base64
    
    # Create basic auth header
    auth_string = f"{credentials.username}:{credentials.password or credentials.token}"
    auth_bytes = auth_string.encode('ascii')
    auth_header = base64.b64encode(auth_bytes).decode('ascii')
    
    headers = {
        "Authorization": f"Basic {auth_header}",
        "Content-Type": "application/json"
    }
    
    validation_url = f"{credentials.url}/artifactory/api/system/ping"
    
    async with aiohttp.ClientSession() as session:
        async with session.get(validation_url, headers=headers) as response:
            if response.status == 200:
                logger.info("JFrog credentials validated successfully")
                return True
            else:
                logger.warning(
                    "JFrog credential validation failed",
                    extra={"status_code": response.status}
                )
                return False


async def _validate_generic_credentials(
    credentials: ServiceCredentials,
    validation_url: Optional[str]
) -> bool:
    """Validate generic service credentials."""
    if not validation_url:
        logger.warning("No validation URL provided for generic credentials")
        return True  # Assume valid if no validation URL
    
    import aiohttp
    
    headers = {
        "Authorization": f"Bearer {credentials.token}",
        **credentials.additional_headers
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.get(validation_url, headers=headers) as response:
            return response.status < 400


def create_test_credentials(service_type: str = "github") -> ServiceCredentials:
    """
    Create test credentials for development and testing.

    Args:
        service_type: Type of service credentials to create

    Returns:
        Test credentials instance
    """
    import os

    if service_type == "github":
        return GitHubCredentials(
            token="test_github_token",
            service_name="github"
        )
    elif service_type == "jenkins":
        # Use environment variables if available, otherwise use defaults
        jenkins_url = os.getenv("JENKINS_URL", "http://localhost:8080")
        jenkins_username = os.getenv("JENKINS_USERNAME", "test_user")
        jenkins_password = os.getenv("JENKINS_PASSWORD", "test_password")
        jenkins_token = os.getenv("JENKINS_TOKEN", "test_jenkins_token")

        return JenkinsCredentials(
            token=jenkins_token,
            api_token=jenkins_token,
            url=jenkins_url,
            username=jenkins_username,
            password=jenkins_password,
            service_name="jenkins"
        )
    elif service_type == "jfrog":
        return JFrogCredentials(
            token="test_jfrog_token",
            url="http://localhost:8081",
            username="test_user",
            service_name="jfrog"
        )
    else:
        return ServiceCredentials(
            service_name=service_type,
            token="test_token"
        )


async def get_jenkins_credentials(tool_context: ToolContext = None) -> JenkinsCredentials:
    """
    Get Jenkins credentials from context or environment.

    Args:
        tool_context: ADK tool context

    Returns:
        Jenkins credentials
    """
    import os

    # Try to get from environment variables first
    jenkins_url = os.getenv("JENKINS_URL")
    jenkins_username = os.getenv("JENKINS_USERNAME")
    jenkins_password = os.getenv("JENKINS_PASSWORD")
    jenkins_token = os.getenv("JENKINS_TOKEN")

    if jenkins_url and jenkins_username and (jenkins_password or jenkins_token):
        return JenkinsCredentials(
            url=jenkins_url,
            username=jenkins_username,
            password=jenkins_password,
            token=jenkins_token,
            service_name="jenkins"
        )

    # Fallback to Secret Manager or test credentials
    try:
        return await get_credentials_from_secret_manager(
            "jenkins-credentials",
            JenkinsCredentials
        )
    except Exception as e:
        logger.warning(f"Failed to get Jenkins credentials: {e}")
        # Return test credentials for development
        return create_test_credentials("jenkins")


async def get_user_identity_from_context(tool_context: ToolContext) -> Optional[Any]:
    """
    Extract user identity from ADK tool context.

    Args:
        tool_context: ADK tool context

    Returns:
        User identity object or None
    """
    try:
        auth_context = getattr(tool_context, 'auth_context', None)
        if auth_context:
            return getattr(auth_context, 'user_identity', None)
        return None
    except Exception as e:
        logger.debug(f"Failed to extract user identity: {e}")
        return None
