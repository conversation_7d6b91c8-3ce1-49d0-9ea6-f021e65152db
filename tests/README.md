# ADK Analyst Tests

This directory contains test suites for the ADK Analyst platform.

## Structure

```
tests/
├── __init__.py                 # Test package initialization
├── README.md                   # This file
├── conftest.py                 # Pytest configuration and fixtures
├── unit/                       # Unit tests
│   ├── test_jenkins_agent.py   # Jenkins agent unit tests
│   ├── test_github_agent.py    # GitHub agent unit tests
│   └── test_shared_utils.py    # Shared utilities tests
├── integration/                # Integration tests
│   ├── test_jenkins_integration.py  # Jenkins API integration
│   └── test_github_integration.py   # GitHub API integration
├── security/                   # Security tests
│   ├── test_auth.py            # Authentication tests
│   └── test_validation.py      # Input validation tests
└── legacy/                     # Legacy test files
    ├── test_cli.py             # Old CLI tests
    └── test_simple_cli.py      # Simple CLI tests
```

## Running Tests

### All Tests
```bash
pytest tests/
```

### Specific Categories
```bash
# Unit tests only
pytest tests/unit/

# Integration tests only
pytest tests/integration/

# Security tests only
pytest tests/security/
```

### With Coverage
```bash
pytest --cov=jenkins_agent --cov=github_agent --cov=shared_utils tests/
```

### Test Markers
```bash
# Run only unit tests
pytest -m unit

# Run only integration tests
pytest -m integration

# Run only security tests
pytest -m security

# Skip slow tests
pytest -m "not slow"
```

## Test Configuration

Tests use pytest with the following configuration:
- Async test support via pytest-asyncio
- Coverage reporting
- Custom markers for test categorization
- Fixtures for common test setup

## Writing Tests

### Unit Tests
- Test individual functions and methods
- Mock external dependencies
- Focus on business logic

### Integration Tests
- Test real API interactions
- Use test credentials/environments
- Verify end-to-end functionality

### Security Tests
- Test authentication and authorization
- Validate input sanitization
- Check for security vulnerabilities

## Test Data

Test data should be:
- Anonymized and safe for version control
- Minimal but representative
- Stored in `tests/data/` if needed

## CI/CD Integration

Tests are designed to run in CI/CD pipelines with:
- Environment variable configuration
- Parallel execution support
- Comprehensive reporting
